---
title: 06-需求优先级排布
description: 深度解析用户增长的核心方法论，包含用户需求分析、渠道选择、病毒裂变、用户留存等关键策略，帮助产品运营人快速掌握增长技能。
keywords: 用户增长,产品运营,增长策略,用户留存,产品营销,裂变策略,数据分析
author: PM面试官
---

# 06-需求优先级排布


## 参考回答：
参考回答：
“我会建立三级评估模型：
1. 战略层：与公司目标强关联性

使用RICE模型（Reach影响力、Impact强度、Confidence信心、Effort成本）量化打分。例如，某需求预计影响10万用户（Reach=3），提升留存5%（Impact=4），数据支撑明确（Confidence=3），需5人周（Effort=2），则优先级分=(3×4×3)/2=18。
2. 用户层：KANO模型分类

基础需求（必须做）：如微信的消息送达功能，不做则产品不可用；

期望需求（线性提升）：如美团外卖的预计送达时间精度，越准体验越好；

兴奋需求（差异化）：如钉钉的‘已读未读’提示，初期形成传播点。
3. 实施层：技术可行性

评估技术ROI：某需求需2个月开发，但能节省20%服务器成本，优先于同等价值但需6个月的需求；

利用‘MVP验证’：对不确定需求先做低成本试点（如灰度发布），再根据数据决策是否投入。”

## 技巧：用分层结构+方法论名称（如NPS、OKR）体现专业性，同时举例说明落地场景。

## 总结
底层逻辑：每个回答需体现“框架+案例+数据”的三角结构，避免抽象描述；

细节设计：适当引用专业术语（如RICE、KANO），但需自然解释其应用场景；

用户思维：站在面试官角度思考——他们希望通过问题考察哪些能力，在回答中针对性“埋点”展示。