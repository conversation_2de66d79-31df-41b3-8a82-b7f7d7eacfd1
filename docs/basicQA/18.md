---
title: 18-如何判断一个需求该做不该做？
---

# 如何判断一个需求该做不该做？

**考点**：需求评估、ROI分析、决策框架  
**参考回答**：  
1. **四维评估模型**：  
   - **用户价值**：  
     - 覆盖面：需求影响多少用户？（如20%日活用户）；  
     - 痛点强度：用户是否愿意付费/花时间解决？（如用户调研中该需求评分8.5/10）。  
   - **商业价值**：  
     - 直接收益：预计提升收入/降低成本的具体数值（如ARPU提升5元）；  
     - 战略价值：是否卡位关键场景（如预防竞品进入）。  
   - **实现成本**：  
     - 开发人日：前后端+测试总耗时（如15人天 vs 其他需求性价比）；  
     - 机会成本：若不做该需求，团队资源可投入哪些更高优先级项目？  
   - **风险**：  
     - 用户体验风险：是否导致操作路径变复杂？（如增加3次点击）；  
     - 合规风险：是否涉及数据隐私问题？  
2. **决策流程**：  
   - **需求评分卡**：各维度加权打分（如用户价值×40% + 商业价值×30% + 成本×20% + 风险×10%），阈值高于80分则通过；  
   - **小成本验证**：若需求不确定性高，先用低代码工具/H5页面快速验证（例如3天开发一个MVP测点击率）。  
3. **案例**：  
   - 美团曾拒绝“社交外卖”功能，因评估后发现会稀释核心交易场景，选择专注优化配送效率。  
**技巧**：  
- 引用“ICE评分模型”（Impact影响度/Confidence信心/Ease实现难度）；  
- 强调“不做需求”也是一种能力（如张小龙对微信克制的功能设计）。