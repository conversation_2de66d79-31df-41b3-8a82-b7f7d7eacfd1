---
title: 12-某功能上线后DAU上涨但GMV下降，如何分析？
---

# 某功能上线后DAU上涨但GMV下降，如何分析？

**考点**：数据分析能力、逻辑归因、商业敏感度  
**参考回答**：  
1. **数据维度拆解**：  
   - **用户分层**：  
     - 新老用户占比变化（如DAU上涨是否由新用户激增导致？新用户7日留存率是否正常？）；  
     - 用户价值分布（如GMV下降是否因低客单价用户占比增加？高价值用户是否流失？）。  
   - **行为路径分析**：  
     - 使用热力图分析新功能使用路径（如用户是否沉迷于娱乐功能而忽略下单？）；  
     - 对比功能上线前后的转化漏斗（如从浏览→加购→支付的转化率是否下降？）。  
   - **外部因素排除**：  
     - 是否同期有竞品补贴活动（如拼多多百亿补贴导致用户比价流失？）；  
     - 是否受季节性影响（如节日后消费疲软）。  
2. **假设验证与解决方案**：  
   - **假设1**：新功能吸引低价值用户（如薅羊毛用户）。  
     - 验证：对比新老用户的ARPU（客单价）、复购率差异；  
     - 方案：优化拉新渠道（如减少低质量渠道投放），增加用户消费引导（如新人专享券）。  
   - **假设2**：功能设计偏离核心场景（如社交功能过重导致交易路径中断）。  
     - 验证：分析用户使用时长分布（如70%时间停留在社交页）；  
     - 方案：在社交页面增加商品弹窗导购，或拆分社交与交易版本进行灰度测试。  
3. **决策优先级**：  
   - 短期：快速回滚高风险功能，减少GMV损失；  
   - 长期：建立功能上线前的“商业影响评估模型”（如预测DAU与GMV的平衡关系）。  
**技巧**：  
- 使用“MECE原则”穷尽所有可能性，避免遗漏关键因素；  
- 展示数据工具使用能力（如SQL查询、Google Analytics事件埋点）。