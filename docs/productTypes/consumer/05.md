---
title: 05-你能描述一次你不得不对产品功能做出艰难决定的经历吗？你是如何处理的？
---

# 你能描述一次你不得不对产品功能做出艰难决定的经历吗？你是如何处理的？

#### 考点
决策能力，处理不确定性和有限信息的情况。

#### 参考回答
在产品开发中，艰难决策不可避免。以下是我的一次经历：

- **背景**  
  我负责一款电商App时，团队计划开发一个“虚拟试穿”功能，用户可通过AR技术试穿商品。开发中期，工程团队反馈技术难度超预期，预计延期2个月，且成本翻倍。同时，用户调研显示只有20%用户明确需要此功能。

- **决策过程**  
  - **数据分析**：我查看了现有AR功能的竞品数据，发现用户使用率平均仅15%，ROI（投资回报率）不高。  
  - **利益相关者沟通**：与工程、设计和市场团队开会，工程强调资源紧张，市场团队则认为该功能能提升品牌形象。  
  - **权衡利弊**：  
    - 优点：创新功能可能吸引媒体关注，提升品牌。  
    - 缺点：高成本、低用户需求，可能拖累其他核心功能开发。  
  - **替代方案**：我提出先上线简版“静态试穿”（上传照片试穿），成本低且能快速验证需求。  
  - **最终决定**：暂停AR功能，优先优化支付流程（数据显示其转化率低），并测试静态试穿。

- **执行与结果**  
  - 静态试穿上线后，使用率达25%，用户反馈正面，且开发仅用3周。  
  - 支付流程优化后，GMV提升10%，证明资源重新分配的正确性。  
  - 市场团队后来用静态试穿做宣传，品牌效果超出预期。

- **反思**  
  - **教训**：决策需基于数据而非假设，MVP是降低风险的有效方式。  
  - **改进**：未来会更早引入用户测试，避免开发到中期才发现需求不足。

这次经历让我学会在不确定性中快速决策，平衡创新与实用，确保资源聚焦于最大价值。

#### 技巧
用STAR法（情境、任务、行动、结果）组织答案，突出数据驱动和团队协作。反思部分能展示你的成长思维。