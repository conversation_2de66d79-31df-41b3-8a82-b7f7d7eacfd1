---
title: 09-描述你在重大系统中断后进行事后分析的流程。
---

# 描述你在重大系统中断后进行事后分析的流程。

**考点：**  
此题考察候选人的事件管理能力、从失败中学习以及改进流程的技能。

**参考回答：**  
在重大系统中断后进行事后分析（Post-Mortem）是改进系统和团队的关键。以下是我的详细流程：

1. **准备工作：**  
   - 在事件解决后立即安排分析会议，通常在24-48小时内。
   - 邀请相关人员，包括工程师、PM和支持团队，确保视角全面。

2. **数据收集：**  
   - 收集所有相关数据，如日志、监控指标（延迟、错误率）和用户投诉。
   - 构建事件时间线，记录故障发生前、中、后的关键节点。

3. **根本原因分析：**  
   - 使用“5个为什么”或鱼骨图方法，深入挖掘根本原因，例如“数据库超载”可能源于“未优化查询”。
   - 避免表面结论，确保找到深层问题。

4. **开放讨论：**  
   - 主持无责讨论，鼓励团队分享观察和建议，例如“当时为何未触发报警”。
   - 营造安全氛围，避免指责，促进坦诚交流。

5. **制定行动项：**  
   - 提出具体改进措施，例如“添加查询索引”或“完善报警规则”。
   - 为每个行动项指定负责人和截止日期，确保执行到位。

6. **文档记录：**  
   - 撰写事后分析报告，包含时间线、根本原因和行动计划。
   - 将报告共享给团队和高层，便于参考和问责。

7. **后续跟踪：**  
   - 定期检查行动项进度，例如每周回顾是否完成优化。
   - 在几周后召开复盘会，评估改进措施的效果。

8. **持续改进：**  
   - 将经验教训融入流程，例如更新事件响应手册或增加自动化测试。
   - 通过培训分享经验，避免类似问题复发。

在一次支付系统中断中，我领导了事后分析，发现原因是流量激增未触发扩容。通过优化自动扩容规则和增加预报警，我们将后续宕机风险降低了80%。这个流程的关键在于数据驱动分析和行动导向，确保每次中断都成为改进的机会，而不是单纯的教训。

**技巧：**  
- 注重学习而非责备，鼓励团队参与。  
- 收集全面数据，确保分析有据可依。  
- 确保行动项具体可行，并跟踪执行。