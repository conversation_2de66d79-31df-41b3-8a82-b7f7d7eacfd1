---
title: 07-如果需求池中有100个需求，你会如何确定优先级？
---

# 如果需求池中有100个需求，你会如何确定优先级？


**考点**：  
1. 需求评估与优先级排序方法论；  
2. 资源分配与ROI分析；  
3. 用户价值与商业价值平衡能力。  

**参考回答**：  
**优先级框架**：  
1. **四象限法**：  
   - **紧急重要**（如系统崩溃修复） → 立即处理；  
   - **重要不紧急**（如核心功能优化） → 规划排期；  
   - **紧急不重要**（如UI微调） → 酌情外包；  
   - **不紧急不重要**（如边缘功能） → 暂缓或拒绝。  

2. **KANO模型**：  
   - **基本型需求**（如微信的消息收发） → 必须满足；  
   - **期望型需求**（如朋友圈编辑功能） → 提升满意度；  
   - **兴奋型需求**（如AR表情） → 差异化竞争。  

3. **ROI评估**：  
   - **开发成本**：估算人力、时间、技术难度；  
   - **预期收益**：量化用户增长、收入提升或效率优化。  
   - 例如，开发“微信支付指纹验证”成本低但安全性提升显著，优先级高。  

**技巧**：  
- **多方协同**：与运营、技术团队对齐优先级标准，避免主观偏差。  
- **动态调整**：定期（如双周）回顾需求池，根据数据反馈重新排序。