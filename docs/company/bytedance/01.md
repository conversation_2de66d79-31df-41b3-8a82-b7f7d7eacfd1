---
title: 01-腾讯
description: 深度解析用户增长的核心方法论，包含用户需求分析、渠道选择、病毒裂变、用户留存等关键策略，帮助产品运营人快速掌握增长技能。
keywords: 用户增长,产品运营,增长策略,用户留存,产品营销,裂变策略,数据分析
author: PM面试官
---

# 01. 简单做个自我介绍

## 考点：结构化表达、核心能力提炼、与岗位的契合度证明

## 参考回答：

- 您好，我是李明，毕业于复旦大学计算机专业，拥有3年B端 SaaS产品经验，目前负责XX公司供应链管理系统的全生命周期管理。
我选择产品经理这一职业，是因为它融合了我对技术逻辑与商业价值的双重兴趣。例如，在主导‘智能仓储调度系统’项目时：

- 用户洞察：通过走访30+仓库管理员，发现传统人工排班效率低的问题，提出算法驱动的动态调度方案；

- 跨团队落地：协调算法团队优化模型，推动UI团队设计可视化看板，最终将仓库人效提升40%，项目获公司年度创新奖；

- 迭代能力：基于用户反馈持续优化，系统上线半年后客户续费率从75%提升至92%。

- 我的优势在于‘从业务场景到技术落地’的闭环能力，以及用数据验证假设的思维习惯。未来希望能在贵公司深入产业互联网领域，创造可量化的用户价值。”

## 技巧
用“总-分-总”结构，将经历与岗位要求强关联，用数据佐证能力，避免流水账。