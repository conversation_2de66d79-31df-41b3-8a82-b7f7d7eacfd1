## Project setup

```bash
$ npm install
```

## Compile and run the project

```bash
# development
$ npm run start

# watch mode
$ npm run start:dev

# production mode
$ npm run start:prod
```

## Run tests

```bash
# unit tests
$ npm run test

# e2e tests
$ npm run test:e2e

# test coverage
$ npm run test:cov
```

## Deployment

When you're ready to deploy your NestJS application to production, there are some key steps you can take to ensure it runs as efficiently as possible. Check out the [deployment documentation](https://docs.nestjs.com/deployment) for more information.

If you are looking for a cloud-based platform to deploy your NestJS application, check out [Mau](https://mau.nestjs.com), our official platform for deploying NestJS applications on AWS. Mau makes deployment straightforward and fast, requiring just a few simple steps:

```bash
$ npm install -g mau
$ mau deploy
```

With Mau, you can deploy your application in just a few clicks, allowing you to focus on building features rather than managing infrastructure.

## Resources

Check out a few resources that may come in handy when working with NestJS:

- Visit the [NestJS Documentation](https://docs.nestjs.com) to learn more about the framework.
- For questions and support, please visit our [Discord channel](https://discord.gg/G7Qnnhy).
- To dive deeper and get more hands-on experience, check out our official video [courses](https://courses.nestjs.com/).
- Deploy your application to AWS with the help of [NestJS Mau](https://mau.nestjs.com) in just a few clicks.
- Visualize your application graph and interact with the NestJS application in real-time using [NestJS Devtools](https://devtools.nestjs.com).
- Need help with your project (part-time to full-time)? Check out our official [enterprise support](https://enterprise.nestjs.com).
- To stay in the loop and get updates, follow us on [X](https://x.com/nestframework) and [LinkedIn](https://linkedin.com/company/nestjs).
- Looking for a job, or have a job to offer? Check out our official [Jobs board](https://jobs.nestjs.com).



===============

http://localhost:3000/



==============

- nest generate module users
- nest generate service users
- nest generate controller users
- http://localhost:3000/users get
- http://localhost:3000/users post
- http://localhost:3000/questions

- curl -X POST -H "Content-Type: application/json" -d '{"name": "Alice", "email": "<EMAIL>"}' http://localhost:3000/users



## 接口

https://pmapi.hjw.zone/questions



============================

## 问题数据导入

### 创建了三个数据导入/管理脚本：
- src/scripts/import-questions.ts: 从markdown文件导入问题到数据库
- src/scripts/check-imported-questions.ts: 检查导入的问题数据
- src/scripts/clean-questions.ts: 清理数据库中的所有问题

#### import-questions.ts 的主要功能：
- 自动读取 `docs/basicQA` 目录下的所有markdown文件
- 解析每个文件中的标题、内容、考点、答案和技巧
- 检查重复问题，避免重复导入
- 将解析的数据保存到数据库中

#### check-imported-questions.ts 的主要功能：
- 显示数据库中问题的总数
- 展示前5个问题的样本数据，包括标题、考点和内容预览

#### clean-questions.ts 的主要功能：
- 删除数据库中所有已存在的问题
- 用于需要重新导入数据时清理数据库

## 脚本使用
#### 清理数据库（如果需要重新导入）：
- npm run clean:questions

#### 导入问题数据：
npm run import:questions

#### 检查导入结果
npm run check:questions


## 执行顺序建议
```
如果是第一次导入数据：
直接运行 npm run import:questions 即可
如果需要重新导入数据：
先运行 npm run clean:questions 清理数据库
然后运行 npm run import:questions 重新导入
最后运行 npm run check:questions 验证导入结果
如果只想查看当前数据库状态：
直接运行 npm run check:questions 即可
注意事项
导入脚本会自动检查重复问题，所以多次运行 import:questions 不会导致数据重复
清理脚本会删除所有问题数据，请谨慎使用
如果导入过程中出现错误，会在控制台显示具体的错误信息
每个脚本执行完成后都会自动关闭数据库连接
这些脚本的设计遵循了您提供的项目规范，使用了 NestJS 的依赖注入系统和数据库抽象层，确保了代码的可维护性和可扩展性。
```

## 更新数据

- npx ts-node clean-questions.ts
- npx ts-node import-questions.ts



## todo
- 持后台主动刷新/失效：如果后台有数据变更，可以主动刷新缓存（如调用 revalidate API）。
- 不同接口不同策略：热点接口缓存时间长，变动频繁的接口缓存时间短。
- 题库数据录入