{"openapi": "3.0.0", "paths": {"/": {"get": {"operationId": "AppController_get<PERSON><PERSON>", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["App"]}}, "/questions": {"post": {"operationId": "QuestionsController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateQuestionDto"}}}}, "responses": {"201": {"description": "问题创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Question"}}}}}, "summary": "创建新问题", "tags": ["questions"]}, "get": {"operationId": "QuestionsController_findAll", "parameters": [{"name": "company", "required": false, "in": "query", "description": "按公司筛选", "schema": {"type": "string"}}, {"name": "category", "required": false, "in": "query", "description": "按分类筛选", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功获取所有问题", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Question"}}}}}}, "summary": "获取所有问题", "tags": ["questions"]}}, "/questions/{id}": {"get": {"operationId": "QuestionsController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "description": "问题ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功获取问题", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Question"}}}}, "404": {"description": "问题不存在"}}, "summary": "获取指定问题", "tags": ["questions"]}, "put": {"operationId": "QuestionsController_update", "parameters": [{"name": "id", "required": true, "in": "path", "description": "问题ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateQuestionDto"}}}}, "responses": {"200": {"description": "问题更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Question"}}}}, "404": {"description": "问题不存在"}}, "summary": "更新问题", "tags": ["questions"]}, "delete": {"operationId": "QuestionsController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "description": "问题ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "问题删除成功"}, "404": {"description": "问题不存在"}}, "summary": "删除问题", "tags": ["questions"]}}, "/users": {"get": {"operationId": "UsersController_findAll", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Users"]}, "post": {"operationId": "UsersController_create", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["Users"]}}, "/users/phone/send-code": {"post": {"operationId": "UsersController_sendVerificationCode", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["Users"]}}, "/users/phone/verify-code": {"post": {"operationId": "UsersController_verifyPhoneCode", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["Users"]}}, "/users/phone/register": {"post": {"operationId": "UsersController_registerWithPhone", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["Users"]}}, "/users/phone/login": {"post": {"operationId": "UsersController_loginWithPhone", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["Users"]}}}, "info": {"title": "产品经理面试题库 API", "description": "产品经理面试题库系统的 API 文档", "version": "1.0", "contact": {}}, "tags": [{"name": "questions", "description": "面试问题相关接口"}, {"name": "users", "description": "用户相关接口"}], "servers": [], "components": {"securitySchemes": {"bearer": {"scheme": "bearer", "bearerFormat": "JWT", "type": "http"}}, "schemas": {"CreateQuestionDto": {"type": "object", "properties": {"title": {"type": "string", "description": "问题标题"}, "content": {"type": "string", "description": "问题内容"}, "keyPoints": {"description": "关键点", "type": "array", "items": {"type": "string"}}, "answer": {"type": "string", "description": "答案"}, "tips": {"type": "string", "description": "技巧提示"}, "company": {"type": "string", "description": "公司名称"}, "category": {"description": "分类", "type": "array", "items": {"type": "string"}}}, "required": ["title", "content", "keyPoints", "answer", "category"]}, "Question": {"type": "object", "properties": {"id": {"type": "string", "description": "问题ID"}, "title": {"type": "string", "description": "问题标题"}, "content": {"type": "string", "description": "问题内容"}, "keyPoints": {"description": "关键点", "type": "array", "items": {"type": "string"}}, "answer": {"type": "string", "description": "答案"}, "tips": {"type": "string", "description": "技巧提示"}, "company": {"type": "string", "description": "公司名称"}, "category": {"description": "分类", "type": "array", "items": {"type": "string"}}, "createdAt": {"format": "date-time", "type": "string", "description": "创建时间"}, "updatedAt": {"format": "date-time", "type": "string", "description": "更新时间"}}, "required": ["id", "title", "content", "keyPoints", "answer", "category", "createdAt", "updatedAt"]}, "UpdateQuestionDto": {"type": "object", "properties": {}}}}}