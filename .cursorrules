## 项目信息
这是一个关于产品经理面试题库以及AI辅助面试的项目。技术框架是nest.js



## 涉及到数据库的操作
- 在'src/databases'实现了一个抽象层，去兼容supabase和数据库typeorm两种查询。
- 所以每张表对用的service文件，对数据库的操作，需要使用'src/interfaces/database.interface'的方法，因为在'src/databases'实现了一个抽象层
- 每个表的entity文件 service文件 controller等文件，需要放在同一个目录下
- 关于时间字段的处理, `src/util/date.util.ts`已经封装了处理的方法

## 接口相关
- 所有接口都需要使用@UseGuards(JwtAuthGuard)进行权限管理

## NestJS 相关规范
### 基本原则

- **使用模块化架构**。  
- **封装 API 于模块**：
  - 每个主要领域/路由一个模块  
  - 一个控制器处理主要路由  
  - 其他控制器处理次要路由  
  - 输出使用简单类型  
  - 每张表对用的service文件，对数据库的操作，需要使用'src/interfaces/database.interface'的方法，因为在'src/databases'实现了一个抽象层
  - 每张表对用的service文件，对数据库的操作，需要使用'src/interfaces/database.interface'的方法，因为在'src/databases'实现了一个抽象层
  

- **核心模块（Core Module）**：
  - 全局异常处理（filters）  
  - 全局中间件（middlewares）  
  - 权限管理（guards）  
  - 请求管理拦截器（interceptors）  

---

## TypeScript 通用指南

### 基本原则

- 注释使用中文
- 所有变量和函数（参数和返回值）都必须声明类型。  
- 避免使用 `any` 类型。  
- 创建必要的类型。  
- 函数内部不要留空行。  
- 每个文件仅导出一个实体。  

### 命名规范

- **类名**使用 PascalCase（帕斯卡命名法）。  
- **变量、函数、方法**使用 camelCase（驼峰命名法）。  
- **文件和目录名**使用 kebab-case（短横线命名法）。  
- **环境变量**使用 UPPERCASE（大写）。  
- 避免使用“魔法数字”，应定义常量。  
- 函数应以**动词**开头。  
- **布尔变量**应以动词开头，如：`isLoading`、`hasError`、`canDelete` 等。  
- 变量和函数命名应使用完整单词，拼写正确，不使用缩写。  
  - 例外：标准缩写（如 `API`、`URL`）  
  - 常见约定缩写：
    - `i, j` 用于循环索引  
    - `err` 用于错误对象  
    - `ctx` 用于上下文  
    - `req, res, next` 用于中间件参数  

### 函数规范

- **函数**（包括方法）应简短、专注于单一功能，少于 20 条指令。  
- 函数命名规则：
  - 以**动词**开头 + 具体描述  
  - 若返回 `boolean`，用 `isX`、`hasX`、`canX` 命名  
  - 若无返回值，用 `executeX`、`saveX` 命名  
- 避免嵌套：
  - 通过**提前检查并返回**减少嵌套  
  - 将逻辑拆分成**工具函数**  
- 使用**高阶函数**（`map`、`filter`、`reduce`）减少嵌套。  
- 简单函数（少于 3 条指令）用**箭头函数**。  
- 复杂函数使用**具名函数**。  
- 使用**默认参数值**避免手动检查 `null` 或 `undefined`。  
- **减少参数数量**，使用 **RO-RO（对象传参 & 返回）**：  
  - 传入多个参数时，使用对象封装  
  - 返回多个结果时，使用对象封装  
  - 为输入参数和输出结果声明类型  
- 保持**单层抽象**。  

### 数据处理

- **避免滥用原始类型**，使用复合类型封装数据。  
- **不要在函数内部执行数据验证**，应使用带内部校验的类。  
- **优先使用不可变数据**。  
- 对于不会变更的数据，使用 `readonly`。  
- 对于不可变的字面量，使用 `as const`。  

### 类

- 遵循 **SOLID 原则**。  
- **优先使用组合，而非继承**。  
- 使用 **接口（interface）** 定义契约。  
- **类应保持精简**：
  - 少于 **200** 条指令  
  - 公共方法少于 **10** 个  
  - 属性少于 **10** 个  

### 异常处理

- **仅对意外错误使用异常**。  
- 捕获异常时，必须要么：
  - 修复**可预见问题**  
  - **添加上下文信息**  
  - 否则，交由全局异常处理  

