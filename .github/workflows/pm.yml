name: Deploy to Vercel

on:
  push:
    branches:
      - main  # 当推送到 main 分支时触发
  pull_request:
    branches:
      - main  # 当创建针对 main 分支的 PR 时触发

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci  # 使用 ci 来确保安装与 package-lock.json 完全一致的依赖

      - name: Build NestJS application
        run: |
          npm run build
        
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          working-directory: ./
          github-token: ${{ secrets.GITHUB_TOKEN }}
