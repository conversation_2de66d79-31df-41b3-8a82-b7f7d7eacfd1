import { <PERSON>ti<PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('favorites')
export class Favorite {
  @ApiProperty({ description: '收藏ID' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: '用户ID' })
  @Column({ name: 'user_id', type: 'integer' })
  userId: number;

  @ApiProperty({ description: '问题ID' })
  @Column({ name: 'question_id', type: 'uuid' })
  questionId: string;

  @ApiProperty({ description: '创建时间' })
  @CreateDateColumn()
  createdAt: string;

  @ApiProperty({ description: '更新时间' })
  @UpdateDateColumn()
  updatedAt: string;
} 