import { CompanyResponseDto } from '../../company/dto/company-response.dto';
import { PmTypeResponseDto } from '../../pm-type/dto/pm-type-response.dto';

export class FavoriteQuestionDto {
  id: string;
  title: string;
  companyName: string;
  pmTypeName: string;
}

export class FavoriteResponseDto {
  id: string;
  userId: number;
  questionId: string;
  createdAt: string;
  updatedAt: string;
  title?: string;
  companyName?: string;
  pmTypeName?: string;
} 