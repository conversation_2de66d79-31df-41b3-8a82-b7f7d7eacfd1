import { Controller, Get, Post, Delete, Param, UseGuards, Request } from '@nestjs/common';
import { FavoritesService } from './favorites.service';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { FavoriteResponseDto } from './dto/favorite-response.dto';

@Controller('favorites')
@UseGuards(JwtAuthGuard)
export class FavoritesController {
  constructor(private readonly favoritesService: FavoritesService) {}

  @Post(':questionId')
  async addFavorite(
    @Request() req,
    @Param('questionId') questionId: string,
  ) {
    return this.favoritesService.addFavorite(req.user.id, questionId);
  }

  @Delete(':questionId')
  async removeFavorite(
    @Request() req,
    @Param('questionId') questionId: string,
  ) {
    return this.favoritesService.removeFavorite(req.user.id, questionId);
  }

  @Get()
  async getUserFavorites(
    @Request() req,
  ): Promise<FavoriteResponseDto[]> {
    return this.favoritesService.getUserFavorites(req.user.id);
  }

  @Get(':questionId/is-favorite')
  async isFavorite(
    @Request() req,
    @Param('questionId') questionId: string,
  ) {
    return this.favoritesService.isFavorite(req.user.id, questionId);
  }
} 