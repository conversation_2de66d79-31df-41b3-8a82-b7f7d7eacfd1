import { Injectable, HttpException, HttpStatus, Inject } from '@nestjs/common';
import { DatabaseService } from '../databases/database.service';
import { DatabaseClient, DATABASE_CLIENT } from '../interfaces/database.interface';
import { Favorite } from './favorite.entity';
import { DateUtils } from '../utils/date.utils';
import { FavoriteResponseDto, FavoriteQuestionDto } from './dto/favorite-response.dto';
import { CompanyResponseDto } from '../company/dto/company-response.dto';
import { PmTypeResponseDto } from '../pm-type/dto/pm-type-response.dto';
import { FindOptionsWhere, ObjectLiteral } from 'typeorm';

export interface FavoriteWithQuestion extends Favorite {
  question?: {
    id: string;
    title: string;
    company: string;
    pmType: string;
  };
}

interface DatabaseFavorite {
  id: string;
  user_id: number;
  question_id: string;
  created_at: Date;
  updated_at: Date;
}

@Injectable()
export class FavoritesService {
  constructor(
    private readonly databaseService: DatabaseService,
    @Inject(DATABASE_CLIENT)
    private readonly databaseClient: DatabaseClient
  ) {}

  /**
   * 添加收藏
   * @param userId 用户ID
   * @param questionId 问题ID
   */
  async addFavorite(userId: number, questionId: string): Promise<Favorite> {
    try {
      // 检查问题是否存在
      const questions = await this.databaseService.find('questions', {
        where: { id: questionId },
      });
      if (questions.length === 0) {
        throw new HttpException('问题不存在', HttpStatus.NOT_FOUND);
      }

      // 检查是否已经收藏
      const existingFavorites = await this.databaseService.find('favorites', {
        where: { user_id: userId, question_id: questionId },
      });
      if (existingFavorites.length > 0) {
        throw new HttpException('已经收藏过这个问题', HttpStatus.BAD_REQUEST);
      }

      const now = DateUtils.getCurrentTime();
      const favorite = await this.databaseService.create('favorites', {
        user_id: userId,
        question_id: questionId,
        created_at: now,
        updated_at: now,
      });
      return this.mapToFavorite(favorite);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      if (error.error?.code === '23505') {
        throw new HttpException('已经收藏过这个问题', HttpStatus.BAD_REQUEST);
      }
      throw new HttpException('添加收藏失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 取消收藏
   * @param userId 用户ID
   * @param questionId 问题ID
   */
  async removeFavorite(userId: number, questionId: string): Promise<void> {
    const deleteCriteria: FindOptionsWhere<ObjectLiteral> = { 
      user_id: userId, 
      question_id: questionId 
    };
    
    const existingFavorite = await this.databaseService.findOne('favorites', { where: deleteCriteria });
    if (!existingFavorite) {
      throw new HttpException('收藏记录不存在', HttpStatus.NOT_FOUND);
    }

    await this.databaseService.delete('favorites', { where: deleteCriteria });
  }

  /**
   * 获取用户的收藏列表
   * @param userId 用户ID
   */
  async getUserFavorites(userId: number): Promise<FavoriteResponseDto[]> {
    try {
      // 判断当前数据库类型
      const dbType = process.env.DB_TYPE || 'supabase';
      let favoritesRaw: any[] = [];
      if (dbType === 'supabase') {
        // Supabase 只 join questions，嵌套 select company/pm_type
        favoritesRaw = await this.databaseClient.query<any>('favorites', {
          where: { user_id: userId },
          join: [
            {
              table: 'questions',
              on: { 'favorites.question_id': 'questions.id' },
              select: ['id', 'title', 'company_id', 'pm_type_id', 'company(name)', 'pm_type(name)']
            }
          ]
        });
      } else {
        // TypeORM 多表 join
        favoritesRaw = await this.databaseClient.query<any>('favorites', {
          where: { user_id: userId },
          join: [
            {
              table: 'questions',
              on: { 'favorites.question_id': 'questions.id' },
              select: ['id', 'title', 'company_id', 'pm_type_id']
            },
            {
              table: 'company',
              on: { 'questions.company_id': 'company.id' },
              select: ['name']
            },
            {
              table: 'pm_type',
              on: { 'questions.pm_type_id': 'pm_type.id' },
              select: ['name']
            }
          ]
        });
      }

      if (!favoritesRaw || favoritesRaw.length === 0) {
        return [];
      }

      // 兼容 TypeORM（getRawMany）和 Supabase（嵌套对象）两种返回结构
      return favoritesRaw.map(f => {
        let title = '';
        let companyName = '';
        let pmTypeName = '';
        // TypeORM: questions_title, company_name, pm_type_name
        // Supabase: questions: { title, company: { name }, pm_type: { name } }
        if (f['questions_title']) title = f['questions_title'];
        else if (f['questions'] && f['questions'].title) title = f['questions'].title;
        if (f['company_name']) companyName = f['company_name'];
        else if (f['questions'] && f['questions'].company && f['questions'].company.name) companyName = f['questions'].company.name;
        if (f['pm_type_name']) pmTypeName = f['pm_type_name'];
        else if (f['questions'] && f['questions'].pm_type && f['questions'].pm_type.name) pmTypeName = f['questions'].pm_type.name;
        return {
          id: f['id'],
          userId: f['user_id'],
          questionId: f['question_id'],
          createdAt: DateUtils.fromDatabaseToISOString(f['created_at']),
          updatedAt: DateUtils.fromDatabaseToISOString(f['updated_at']),
          title,
          companyName,
          pmTypeName
        };
      });
    } catch (error) {
      console.error('Error in getUserFavorites:', error);
      throw new HttpException('获取收藏列表失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 检查用户是否已收藏该问题
   * @param userId 用户ID
   * @param questionId 问题ID
   */
  async isFavorite(userId: number, questionId: string): Promise<boolean> {
    const favoriteRecord = await this.databaseService.findOne('favorites', {
      where: { user_id: userId, question_id: questionId },
    });
    return !!favoriteRecord;
  }

  private mapToFavorite(item: any): Favorite {
    return {
      id: item.id,
      userId: item.user_id,
      questionId: item.question_id,
      createdAt: DateUtils.fromDatabaseToISOString(item.created_at),
      updatedAt: DateUtils.fromDatabaseToISOString(item.updated_at),
    };
  }
} 