import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { QuestionsService } from '../questions/questions.service';

async function bootstrap() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const questionsService = app.get(QuestionsService);

  try {
    const result = await questionsService.findAll({ limit: 1000 });
    console.log(`Found ${result.meta.total} questions to delete`);

    for (const question of result.data) {
      await questionsService.remove(question.id);
      console.log(`Deleted question: ${question.title}`);
    }

    console.log('Cleanup completed successfully');
  } catch (error) {
    console.error('Error cleaning questions:', error);
  } finally {
    await app.close();
  }
}

bootstrap(); 