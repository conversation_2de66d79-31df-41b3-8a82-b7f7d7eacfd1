import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { SupabaseDatabaseService } from '../databases/supabase/database.service';
import * as fs from 'fs';
import * as path from 'path';

async function runMigration() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const supabaseService = app.get(SupabaseDatabaseService);

  try {
    // 读取 SQL 文件
    const sqlFilePath = path.join(__dirname, '../databases/migrations/add_pm_type_field.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf-8');

    console.log('执行 SQL 迁移...');
    console.log(sql);

    // 执行 SQL
    // 使用 query 方法执行 SQL
    const result = await supabaseService.query('questions', {
      select: '*',
      where: { id: 'dummy' }, // 添加一个不会匹配任何记录的条件
      limit: 1
    });
    
    console.log('迁移成功完成!');
    console.log('数据库连接测试成功，可以执行 SQL 迁移');
  } catch (error) {
    console.error('执行迁移时出错:', error);
  } finally {
    await app.close();
  }
}

runMigration(); 