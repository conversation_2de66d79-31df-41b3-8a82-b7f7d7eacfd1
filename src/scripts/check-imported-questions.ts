import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { QuestionsService } from '../questions/questions.service';

async function bootstrap() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const questionsService = app.get(QuestionsService);

  try {
    const result = await questionsService.findAll();
    console.log(`Total questions imported: ${result.meta.total}`);
    console.log('\nFirst 5 questions:');
    result.data.slice(0, 5).forEach((question, index) => {
      console.log(`\n${index + 1}. ${question.title}`);
      console.log(`   Company: ${question.companyName}`);
      console.log(`   Category: ${question.category?.join(', ')}`);
      console.log(`   PM Type: ${question.pmTypeName}`);
    });
  } catch (error) {
    console.error('Error checking imported questions:', error);
  } finally {
    await app.close();
  }
}

bootstrap(); 