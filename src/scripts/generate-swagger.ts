import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import * as fs from 'fs';
import * as path from 'path';

async function generateSwaggerJson() {
  const app = await NestFactory.create(AppModule);
  
  const config = new DocumentBuilder()
    .setTitle('产品经理面试题库 API')
    .setDescription('产品经理面试题库系统的 API 文档')
    .setVersion('1.0')
    .addTag('questions', '面试问题相关接口')
    .addTag('users', '用户相关接口')
    .addBearerAuth()
    .build();
  
  const document = SwaggerModule.createDocument(app, config);
  
  // 确保输出目录存在
  const outputPath = path.resolve(process.cwd(), 'swagger');
  if (!fs.existsSync(outputPath)) {
    fs.mkdirSync(outputPath);
  }
  
  // 写入 swagger.json 文件
  fs.writeFileSync(
    path.join(outputPath, 'swagger.json'),
    JSON.stringify(document, null, 2),
  );
  
  console.log('Swagger JSON has been generated successfully!');
  await app.close();
}

generateSwaggerJson(); 