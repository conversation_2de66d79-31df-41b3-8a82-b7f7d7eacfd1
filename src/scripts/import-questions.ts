import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { QuestionsService } from '../questions/questions.service';
import * as fs from 'fs';
import * as path from 'path';
import * as matter from 'gray-matter';

interface ImportConfig {
  directory: string;
  companyId?: number;
  pmTypeId?: number;
}

async function importQuestions() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const questionsService = app.get(QuestionsService);

  // 获取项目根目录
  const rootDir = path.resolve(__dirname, '../..');
  
  // 创建输出目录
  const outputDir = path.join(rootDir, 'output');
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  // 定义导入配置
  const importConfigs: ImportConfig[] = [
    { directory: path.join(rootDir, 'docs/basicQA') },
    { directory: path.join(rootDir, 'docs/company/ali'), companyId: 1 }, // 阿里巴巴
    { directory: path.join(rootDir, 'docs/company/tencent'), companyId: 2 }, // 腾讯
    { directory: path.join(rootDir, 'docs/company/bytedance'), companyId: 3 }, // 字节跳动
    // 添加产品类型目录
    { directory: path.join(rootDir, 'docs/productTypes/ai'), pmTypeId: 1 }, // AI产品经理
    { directory: path.join(rootDir, 'docs/productTypes/consumer'), pmTypeId: 2 }, // C端产品经理
    { directory: path.join(rootDir, 'docs/productTypes/midbackend'), pmTypeId: 3 }, // 中后台产品经理
    { directory: path.join(rootDir, 'docs/productTypes/sass'), pmTypeId: 4 }, // SASS产品经理
  ];

  let totalImported = 0;

  for (const config of importConfigs) {
    if (!fs.existsSync(config.directory)) {
      console.log(`目录不存在: ${config.directory}`);
      continue;
    }

    const files = fs.readdirSync(config.directory)
      .filter(file => file.endsWith('.md'));

    console.log(`\n处理目录: ${config.directory}`);
    console.log(`找到 ${files.length} 个markdown文件`);

    for (const file of files) {
      const filePath = path.join(config.directory, file);
      const content = fs.readFileSync(filePath, 'utf-8');
      const { data: frontMatter, content: markdownContent } = matter(content);

      // 从文件名中提取标题（如果frontMatter中没有）
      const title = frontMatter.title || path.basename(file, '.md');
      // 移除开头的数字和短横线
      const cleanedTitle = title.replace(/^\d+-/, '').trim();

      // 提取关键点（从markdown内容中查找加粗文本）
      const keyPoints = markdownContent
        .split('\n')
        .filter(line => line.includes('**'))
        .map(line => line.replace(/\*\*/g, '').trim())
        .filter(Boolean);

      // 提取答案（查找"参考回答："后的内容）
      let answer = '';
      let isAnswerSection = false;
      let answerLines: string[] = [];
      
      for (const line of markdownContent.split('\n')) {
        // 如果找到参考回答标记，开始收集答案
        if (line.includes('参考回答') || line.includes('参考答案')) {
          isAnswerSection = true;
          // 如果答案标记在同一行，提取该行后面的内容
          const answerContent = line.split(/参考(?:回答|答案)[：:]\s*/)[1]?.trim();
          if (answerContent) {
            // 去除Markdown标记
            const cleanedContent = answerContent.replace(/\*\*/g, '').replace(/\*/g, '').trim();
            if (cleanedContent) {
              answerLines.push(cleanedContent);
            }
          }
          continue;
        }
        
        // 如果已经在收集答案内容
        if (isAnswerSection) {
          // 如果遇到技巧部分，停止收集
          if (line.includes('技巧：') || line.includes('技巧：')) {
            break;
          }
          
          // 跳过空行和以#开头的行
          if (line.trim() && !line.startsWith('#')) {
            // 去除Markdown标记
            const cleanedLine = line.replace(/\*\*/g, '').replace(/\*/g, '').trim();
            if (cleanedLine) {
              answerLines.push(cleanedLine);
            }
          }
        }
      }
      
      // 将所有收集到的答案行合并
      answer = answerLines.join('\n').trim();

      // 输出答案到本地文件
      const answerOutputFile = path.join(outputDir, `${path.basename(file, '.md')}_answer.txt`);
      fs.writeFileSync(answerOutputFile, `标题: ${cleanedTitle}\n\n答案:\n${answer}\n\n`);

      console.log(`已输出答案到: ${answerOutputFile}`);

      // 提取技巧（查找"技巧："后的内容）
      const tipsMatch = markdownContent.match(/技巧：\s*([\s\S]*?)(?=\n\n|$)/);
      const tips = tipsMatch ? tipsMatch[1].trim() : '';

      // 提取考点（从frontMatter或内容中）
      let category = '';
      
      // 首先尝试从frontMatter中获取考点
      if (frontMatter.keyPoints) {
        category = frontMatter.keyPoints;
      } else {
        // 尝试从内容中提取考点
        category = extractKeyPoints(markdownContent);
      }

      // 将字符串转换为数组
      const keyPointsArray = category ? category.split('、').filter(Boolean) : [];
      
      // 将提取到的考点输出到本地文件
      const keypointsOutputFile = path.join(outputDir, `${path.basename(file, '.md')}_keypoints.txt`);
      fs.writeFileSync(keypointsOutputFile, `标题: ${cleanedTitle}\n考点: ${category}\n\n`);

      // 输出title到本地文件进行验证
      const titleOutputFile = path.join(outputDir, `${path.basename(file, '.md')}_title.txt`);
      fs.writeFileSync(titleOutputFile, `原始标题: ${title}\n清理后标题: ${cleanedTitle}\n\n`);

      try {
        await questionsService.create({
          title: cleanedTitle,
          content: markdownContent,
          keyPoints: keyPointsArray,
          answer,
          tips,
          companyId: config.companyId,
          category: keyPointsArray,
          pmTypeId: config.pmTypeId,
        });
        totalImported++;
        console.log(`成功导入: ${cleanedTitle}`);
      } catch (error) {
        console.error(`导入失败 ${cleanedTitle}:`, error.message);
      }
    }
  }

  console.log(`\n导入完成！共导入 ${totalImported} 个问题`);
  await app.close();
}

function extractKeyPoints(content: string): string {
  const lines = content.split('\n');
  let keyPoints: string[] = [];
  let foundKeyPoints = false;
  let isCollecting = false;

  for (let line of lines) {
    const trimmedLine = line.trim();
    
    // 如果找到考点标记（包括带有##的情况）
    if (trimmedLine.includes('考点') || trimmedLine.includes('**考点**')) {
      foundKeyPoints = true;
      isCollecting = true;
      // 如果考点在同一行，直接提取
      const keyPointContent = trimmedLine.split(/考点[：:]\s*/)[1]?.trim();
      if (keyPointContent) {
        // 移除开头的#号、*号和空格
        keyPoints.push(keyPointContent.replace(/^[#*\s]+/, '').replace(/[#*\s]+$/, '').replace(/\*/g, ''));
      }
      continue;
    }

    // 如果已经在收集考点内容
    if (isCollecting) {
      // 如果遇到参考回答或其他标题，停止收集
      if (trimmedLine.includes('参考回答') || trimmedLine.startsWith('##')) {
        break;
      }
      
      // 跳过空行、以#开头的行和只包含*的行
      if (trimmedLine && !trimmedLine.startsWith('#') && !/^\**$/.test(trimmedLine)) {
        // 处理编号格式的考点（如：1. xxx；）
        const numberedPoint = trimmedLine.match(/^\d+\.\s*(.+?)[；;]?\s*$/);
        if (numberedPoint) {
          keyPoints.push(numberedPoint[1].replace(/\*/g, '').trim());
        } else {
          // 如果不是编号格式，作为普通文本处理
          const cleanedLine = trimmedLine.replace(/\*/g, '').trim();
          if (cleanedLine) {
            keyPoints.push(cleanedLine);
          }
        }
      }
    }
  }

  // 如果在frontmatter中找不到考点，尝试从内容中提取
  if (!foundKeyPoints) {
    const keyPointsMatch = content.match(/考点[：:]\s*([^]*?)(?=\n\s*(?:##|参考回答))/);
    if (keyPointsMatch) {
      const extractedPoints = keyPointsMatch[1].trim()
        .split('\n')
        .map(line => {
          // 处理编号格式
          const numberedPoint = line.match(/^\d+\.\s*(.+?)[；;]?\s*$/);
          if (numberedPoint) {
            return numberedPoint[1].replace(/\*/g, '').trim();
          }
          return line.replace(/^[#*\s]+/, '').replace(/[#*\s]+$/, '').replace(/\*/g, '').trim();
        })
        .filter(Boolean);
      keyPoints.push(...extractedPoints);
    }
  }

  // 将所有考点用顿号连接
  return keyPoints.filter(Boolean).join('、');
}

importQuestions().catch(console.error); 