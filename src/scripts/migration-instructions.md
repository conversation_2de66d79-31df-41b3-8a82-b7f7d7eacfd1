# 数据库迁移说明

## 添加 pmType 字段到 questions 表

### 方法一：使用 Supabase 仪表板

1. 登录 Supabase 仪表板
2. 选择项目
3. 点击左侧菜单中的 "SQL Editor"
4. 创建新的查询
5. 粘贴以下 SQL 并执行：

```sql
-- 添加 pmType 字段到 questions 表
ALTER TABLE questions ADD COLUMN IF NOT EXISTS "pmType" TEXT;

-- 添加注释
COMMENT ON COLUMN questions."pmType" IS '产品经理类型，如C端产品经理、AI产品经理等';
```

### 方法二：使用 Supabase CLI

如果你已安装 Supabase CLI，可以运行以下命令：

```bash
supabase db push
```

确保 `supabase/migrations` 目录中包含相应的迁移文件。

## 验证迁移

迁移完成后，可以通过 API 验证字段是否已添加：

```bash
curl "http://localhost:3000/questions?page=1&limit=1" | json_pp
```

检查返回的 JSON 中是否包含 `pmType` 字段。 