import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

export interface Response<T> {
  code: number;
  data: T;
  msg: string;
}

@Injectable()
export class TransformInterceptor<T> implements NestInterceptor<T, Response<T>> {
  intercept(context: ExecutionContext, next: CallHandler): Observable<Response<T>> {
    return next.handle().pipe(
      map(data => {
        // 如果数据已经是标准格式，直接返回
        if (data && typeof data === 'object' && 'code' in data && 'data' in data && 'msg' in data) {
          return data;
        }
        
        // 如果数据是数组，直接包装
        if (Array.isArray(data)) {
          return {
            code: 0,
            data,
            msg: 'success'
          };
        }
        
        // 如果数据是对象且包含 data 属性，递归解包
        if (data && typeof data === 'object' && 'data' in data) {
          let unwrappedData = data.data;
          
          // 递归解包嵌套的 data 属性
          while (unwrappedData && typeof unwrappedData === 'object' && 'data' in unwrappedData) {
            unwrappedData = unwrappedData.data;
          }
          
          return {
            code: 0,
            data: unwrappedData,
            msg: 'success'
          };
        }
        
        // 其他情况，直接包装数据
        return {
          code: 0,
          data,
          msg: 'success'
        };
      }),
    );
  }
} 