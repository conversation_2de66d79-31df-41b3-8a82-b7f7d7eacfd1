import { Injectable, NotFoundException } from '@nestjs/common';
import { DatabaseService } from '../databases/database.service';
import { FindOptionsWhere } from '../interfaces/database.interface';
import { Company } from './company.entity';
import { CreateCompanyDto } from './dto/create-company.dto';
import { CompanyWithCountDto } from './dto/company-with-count.dto';
import { CompanyResponseDto } from './dto/company-response.dto';
import { ObjectLiteral } from 'typeorm';

@Injectable()
export class CompanyService {
  constructor(
    private readonly databaseService: DatabaseService,
  ) {}

  // 创建公司
  async create(createCompanyDto: CreateCompanyDto): Promise<CompanyResponseDto> {
    const result = await this.databaseService.create('company', {
      ...createCompanyDto,
      show: 1
    });
    return this.mapToResponseDto(result as Company);
  }

  // 获取所有公司
  async findAll(): Promise<CompanyResponseDto[]> {
    const result = await this.databaseService.find('company', {
      where: { show: 1 }
    });
    return (result as Company[]).map(this.mapToResponseDto);
  }

  // 根据ID获取公司
  async findOne(id: number): Promise<CompanyResponseDto> {
    const result = await this.databaseService.findOne<Company>('company', { where: { id: id } as FindOptionsWhere<Company> });
    
    // Check if result exists and is visible
    if (!result || result.show !== 1) { 
      throw new NotFoundException(`ID为 ${id} 的公司不存在或不可见`);
    }
    // Map to DTO before returning
    return this.mapToResponseDto(result);
  }

  // 更新公司信息
  async update(id: number, updateCompanyDto: CreateCompanyDto): Promise<CompanyResponseDto> {
    await this.databaseService.update('company', id.toString(), updateCompanyDto);
    return this.findOne(id);
  }

  // 删除公司（软删除，将 show 设置为 0）
  async remove(id: number): Promise<void> {
    try {
      await this.databaseService.update('company', id.toString(), { show: 0 });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new NotFoundException(`Company with ID ${id} not found`);
      }
      throw error;
    }
  }

  // 获取所有公司及其问题数量
  async findAllWithCount(): Promise<CompanyWithCountDto[]> {
    const dbType = process.env.DB_TYPE || 'supabase';
    if (dbType === 'supabase') {
      // Supabase: 嵌套 select questions(id) 统计数量
      const companies = await this.databaseService.find<any>('company', {
        where: { show: 1 },
        join: [
          {
            table: 'questions',
            on: { 'company.id': 'questions.company_id' },
            select: ['id']
          }
        ]
      });
      return companies.map((item: any) => ({
        id: item.id,
        name: item.name,
        questionCount: Array.isArray(item.questions) ? item.questions.length : 0
      }));
    } else {
      // TypeORM: left join + group by
      const raw = await this.databaseService.find<any>('company', {
        where: { show: 1 },
        join: [
          {
            table: 'questions',
            on: { 'company.id': 'questions.company_id' },
            type: 'left',
            select: []
          }
        ],
        select: 'company.id, company.name, COUNT(questions.id) as question_count',
        orderBy: { 'id': 'asc' }
      });
      return raw.map((item: any) => ({
        id: item.id,
        name: item.name,
        questionCount: Number(item.question_count) || 0
      }));
    }
  }

  // 将 Company 实体转换为 CompanyResponseDto
  private mapToResponseDto(company: Company): CompanyResponseDto {
    return {
      id: company.id,
      name: company.name,
    };
  }
} 