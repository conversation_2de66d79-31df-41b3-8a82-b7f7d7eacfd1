import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import * as dotenv from 'dotenv';
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';

dotenv.config();

export const typeOrmConfig: TypeOrmModuleOptions = {
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: Number(process.env.DB_PORT) || 5432,
//   username: process.env.DB_USER || 'postgres',
username: process.env.DB_USER || 'sail',
  password: process.env.DB_PASS || '123456',
  database: process.env.DB_NAME || 'pmServer',
  autoLoadEntities: true,
  synchronize: true,  // 生产环境请设置为 false
  namingStrategy: new SnakeNamingStrategy()
};
