curl -X POST http://localhost:3000/questions \
-H "Content-Type: application/json" \
-d '{
  "title": "产品经理如何做竞品分析？",
  "content": "请详细描述你做竞品分析的方法论和步骤，包括数据收集方式、分析维度等。",
  "keyPoints": [
    "竞品分析方法论",
    "数据收集能力",
    "分析维度的选择",
    "洞察能力"
  ],
  "answer": "一个好的竞品分析应该包含以下步骤：\n1. 确定分析目标：明确为什么要做竞品分析，是新产品调研还是现有产品优化\n2. 选择竞品范围：直接竞争对手、间接竞争对手、标杆产品\n3. 设计分析维度：\n   - 产品定位和目标用户\n   - 核心功能对比\n   - 用户体验\n   - 商业模式\n   - 技术实现\n4. 数据收集：\n   - 产品亲测体验\n   - 用户评价收集\n   - 运营数据分析\n5. 总结和建议：\n   - 竞品优劣势分析\n   - 市场机会点发现\n   - 产品建议输出",
  "tips": "回答这个问题时，建议：\n1. 展示系统化思维\n2. 结合实际案例\n3. 强调数据驱动\n4. 突出用户价值",
  "company": "字节跳动",
  "category": ["C端产品", "竞品分析"]
}'