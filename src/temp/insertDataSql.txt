## 往question表插入数据的sql

INSERT INTO questions (
    id,
    title,
    content,
    "keyPoints",
    answer,
    tips,
    company,
    category,
    "createdAt",
    "updatedAt"
) 
VALUES 
    (
        uuid_generate_v4(),
        '如何制定产品需求优先级？',
        '作为产品经理，如何在众多需求中确定优先级？请详细说明判断标准和方法论。',
        ARRAY['业务价值', '开发成本', '用户需求', '战略契合度'],
        '制定需求优先级可以从以下几个维度考虑：1. 业务价值（ROI）2. 用户需求程度 3. 技术实现难度 4. 战略方向契合度 5. 时间紧迫度。可以使用KANO模型或优先级矩阵等工具辅助决策。具体实施时，可以采用打分制，对各个维度进行量化评估。',
        '建议结合实际案例说明，展示决策思路',
        'ByteDance',
        ARRAY['产品经理', '需求管理', '决策能力'],
        NOW(),
        NOW()
    ),
    (
        uuid_generate_v4(),
        '如何进行竞品分析？',
        '请详细描述作为产品经理如何系统地进行竞品分析，包括分析维度和方法。',
        ARRAY['市场定位', '功能对比', '用户体验', '商业模式'],
        '竞品分析方法：1. 确定竞品范围（直接竞品、间接竞品）2. 多维度分析（产品功能、用户体验、商业模式、技术实现）3. 优劣势分析（SWOT分析）4. 差异化策略制定 5. 持续跟踪竞品动态。建议建立竞品分析矩阵，定期更新。',
        '注意要从用户视角出发，而不是纯功能堆砌',
        'Alibaba',
        ARRAY['产品经理', '竞品分析', '市场研究'],
        NOW(),
        NOW()
    ),
    (
        uuid_generate_v4(),
        '产品经理如何与开发团队有效沟通？',
        '在日常工作中，产品经理如何与开发团队进行有效沟通，确保产品顺利落地？',
        ARRAY['沟通技巧', '文档规范', '团队协作', '需求管理'],
        '有效沟通要点：1. 制定清晰的产品文档规范（PRD、原型图）2. 建立固定的沟通机制（站会、评审会）3. 学习基础技术知识，理解开发难点 4. 及时跟进开发进度，协调资源 5. 建立产品需求池，提前规划排期。',
        '要站在开发团队角度思考问题，理解技术约束',
        'Tencent',
        ARRAY['产品经理', '团队协作', '沟通能力'],
        NOW(),
        NOW()
    );