/**
 * 日期时间工具类
 */
export class DateUtils {
  /**
   * 获取当前时间（UTC+8）
   * @returns 返回当前时间的 ISO 字符串（UTC+8）
   */
  static getCurrentTime(): string {
    const now = new Date();
    // 添加 8 小时以匹配 UTC+8
    now.setHours(now.getHours() + 8);
    return now.toISOString();
  }

  /**
   * 将数据库时间转换为 Date 对象
   * @param dateString 数据库返回的时间字符串或 Date 对象
   * @returns Date 对象
   */
  static fromDatabase(dateString: string | Date): Date {
    if (dateString instanceof Date) {
      return dateString;
    }
    return new Date(dateString);
  }

  /**
   * 将数据库时间转换为 ISO 字符串
   * @param dateString 数据库返回的时间字符串或 Date 对象
   * @returns ISO 字符串
   */
  static fromDatabaseToISOString(dateString: string | Date): string {
    return this.fromDatabase(dateString).toISOString();
  }

  /**
   * 将 Date 对象转换为数据库时间字符串
   * @param date Date 对象
   * @returns ISO 字符串（UTC+8）
   */
  static toDatabase(date: Date): string {
    const newDate = new Date(date);
    // 添加 8 小时以匹配 UTC+8
    newDate.setHours(newDate.getHours() + 8);
    return newDate.toISOString();
  }
} 