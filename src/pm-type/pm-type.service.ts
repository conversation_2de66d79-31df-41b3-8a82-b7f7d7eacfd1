import { Injectable, NotFoundException } from '@nestjs/common';
import { PmType } from './pm-type.entity';
import { CreatePmTypeDto } from './dto/create-pm-type.dto';
import { DatabaseService } from '../databases/database.service';
import { PmTypeWithCountDto } from './dto/pm-type-with-count.dto';
import { PmTypeResponseDto } from './dto/pm-type-response.dto';
import { FindOptionsWhere } from '../interfaces/database.interface';

@Injectable()
export class PmTypeService {
  constructor(
    private readonly databaseService: DatabaseService,
  ) {}

  // 创建PM类型
  async create(createPmTypeDto: CreatePmTypeDto): Promise<PmTypeResponseDto> {
    const result = await this.databaseService.create('pm_type', {
      ...createPmTypeDto,
      show: 1
    });
    return this.mapToResponseDto(result as PmType);
  }

  // 获取所有PM类型
  async findAll(): Promise<PmTypeResponseDto[]> {
    const result = await this.databaseService.find('pm_type', {
      where: { show: 1 }
    });
    return (result as PmType[]).map(this.mapToResponseDto);
  }

  // 根据ID获取PM类型
  async findOne(id: number): Promise<PmTypeResponseDto> {
    const result = await this.databaseService.findOne<PmType>('pm_type', { where: { id: id } as FindOptionsWhere<PmType> });
    if (!result || result.show !== 1) {
      throw new NotFoundException(`ID为 ${id} 的产品经理类型不存在或不可见`);
    }
    return this.mapToResponseDto(result);
  }

  // 更新PM类型信息
  async update(id: number, updatePmTypeDto: CreatePmTypeDto): Promise<PmTypeResponseDto> {
    await this.databaseService.update('pm_type', id.toString(), updatePmTypeDto);
    return this.findOne(id);
  }

  // 删除PM类型（软删除，将 show 设置为 0）
  async remove(id: number): Promise<void> {
    try {
      await this.databaseService.update('pm_type', id.toString(), { show: 0 });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new NotFoundException(`PM Type with ID ${id} not found`);
      }
      throw error;
    }
  }

  // 获取所有PM类型及其问题数量
  async findAllWithCount(): Promise<PmTypeWithCountDto[]> {
    const dbType = process.env.DB_TYPE || 'supabase';
    if (dbType === 'supabase') {
      // Supabase: 嵌套 select questions(id) 统计数量
      const pmTypes = await this.databaseService.find<any>('pm_type', {
        where: { show: 1 },
        join: [
          {
            table: 'questions',
            on: { 'pm_type.id': 'questions.pm_type_id' },
            select: ['id']
          }
        ]
      });
      return pmTypes.map((item: any) => ({
        id: item.id,
        name: item.name,
        questionCount: Array.isArray(item.questions) ? item.questions.length : 0
      }));
    } else {
      // TypeORM: left join + group by
      const raw = await this.databaseService.find<any>('pm_type', {
        where: { show: 1 },
        join: [
          {
            table: 'questions',
            on: { 'pm_type.id': 'questions.pm_type_id' },
            type: 'left',
            select: []
          }
        ],
        select: 'pm_type.id, pm_type.name, COUNT(questions.id) as question_count',
        orderBy: { 'id': 'asc' }
      });
      return raw.map((item: any) => ({
        id: item.id,
        name: item.name,
        questionCount: Number(item.question_count) || 0
      }));
    }
  }

  // 将 PmType 实体转换为 PmTypeResponseDto
  private mapToResponseDto(pmType: PmType): PmTypeResponseDto {
    return {
      id: pmType.id,
      name: pmType.name,
      // description: pmType.description, // Assuming no description or handled if exists
    };
  }
} 