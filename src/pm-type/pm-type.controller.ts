import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, ParseIntPipe } from '@nestjs/common';
import { PmTypeService } from './pm-type.service';
import { CreatePmTypeDto } from './dto/create-pm-type.dto';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { PmTypeWithCountDto } from './dto/pm-type-with-count.dto';

@Controller('pm-type')
@UseGuards(JwtAuthGuard)
export class PmTypeController {
  constructor(private readonly pmTypeService: PmTypeService) {}

  @Post()
  create(@Body() createPmTypeDto: CreatePmTypeDto) {
    return this.pmTypeService.create(createPmTypeDto);
  }

  @Get()
  findAll() {
    return this.pmTypeService.findAll();
  }

  @Get('with-count')
  async findAllWithCount(): Promise<PmTypeWithCountDto[]> {
    return this.pmTypeService.findAllWithCount();
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.pmTypeService.findOne(id);
  }

  @Patch(':id')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updatePmTypeDto: CreatePmTypeDto
  ) {
    return this.pmTypeService.update(id, updatePmTypeDto);
  }

  @Delete(':id')
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.pmTypeService.remove(id);
  }
} 