// interfaces/database.interface.ts
import { ObjectLiteral as TypeOrmObjectLiteral, FindOptionsWhere as TypeOrmFindOptionsWhere } from 'typeorm';

// Ensure FindOptionsWhere is exported
export type FindOptionsWhere<T> = TypeOrmFindOptionsWhere<T>;
// Re-export ObjectLiteral
export type ObjectLiteral = TypeOrmObjectLiteral;

export const DATABASE_CLIENT = 'DATABASE_CLIENT';

// join 查询参数接口
export interface JoinOption {
  /**
   * 需要 join 的表名
   */
  table: string;
  /**
   * 可选，表别名
   */
  alias?: string;
  /**
   * join 类型，默认 left
   */
  type?: 'inner' | 'left';
  /**
   * 连接条件，如 { 'questions.company_id': 'company.id' }
   */
  on: Record<string, string>;
  /**
   * 需要从 join 表中选出的字段
   */
  select?: string[];
}

export interface DatabaseClient {
  query<T extends ObjectLiteral>(table: string, options?: {
    select?: string;
    where?: Record<string, any>;
    order?: { column: string; ascending?: boolean };
    limit?: number;
    offset?: number;
    /**
     * join 查询参数数组
     */
    join?: JoinOption[];
  }): Promise<T[]>;
  
  insert<T extends ObjectLiteral>(table: string, data: Partial<T>): Promise<T>;
  
  update<T extends ObjectLiteral>(table: string, id: string, data: Partial<T>): Promise<T>;
  
  delete<T extends ObjectLiteral>(table: string, id: string): Promise<void>;

  count(table: string, options?: {
    where?: Record<string, any>;
  }): Promise<number>;
}

export interface DatabaseService {
  create<T extends ObjectLiteral>(entityName: string, data: Partial<T>): Promise<T>;
  find<T extends ObjectLiteral>(entityName: string, options?: { 
    where?: FindOptionsWhere<T>; 
    limit?: number; 
    offset?: number; 
    orderBy?: Record<string, 'asc' | 'desc'>;
    select?: string;
    join?: JoinOption[];
  }): Promise<T[]>;
  findOne<T extends ObjectLiteral>(entityName: string, options?: { where?: FindOptionsWhere<T> }): Promise<T | null>;
  update<T extends ObjectLiteral>(entityName: string, id: string, data: Partial<T>): Promise<T>;
  delete<T extends ObjectLiteral>(entityName: string, options: { where: FindOptionsWhere<T> }): Promise<void>;
  count(table: string, where?: Record<string, any>): Promise<number>;
}