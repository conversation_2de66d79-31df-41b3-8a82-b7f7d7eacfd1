import { Logger } from '@nestjs/common';
import { fieldMappings, TableName, FieldMapping } from '../config/field-mappings.config';

export class FieldMapper {
  private static readonly logger = new Logger(FieldMapper.name);

  /**
   * 将对象中的字段名转换为数据库字段名
   * @param table 表名
   * @param data 要转换的数据对象
   * @returns 转换后的数据对象
   */
  static toDatabase<T extends Record<string, any>>(table: string, data: T): Record<string, any> {
    this.logger.debug(`开始转换表 ${table} 的数据到数据库格式`);
    this.logger.debug(`原始数据: ${JSON.stringify(data)}`);

    const mappings = fieldMappings[table as TableName];
    if (!mappings) {
      this.logger.debug(`表 ${table} 没有定义字段映射，返回原始数据`);
      return data;
    }

    const result = Object.entries(data).reduce((acc, [key, value]) => {
      const dbField = mappings[key as keyof FieldMapping] || key;
      acc[dbField] = value;
      this.logger.debug(`字段映射: ${key} -> ${dbField}`);
      return acc;
    }, {} as Record<string, any>);

    return result;
  }

  /**
   * 将数据库字段名转换为对象字段名
   * @param table 表名
   * @param data 要转换的数据对象
   * @returns 转换后的数据对象
   */
  static toEntity<T extends Record<string, any>>(table: string, data: T): Record<string, any> {
    this.logger.debug(`开始转换表 ${table} 的数据到实体格式`);
    this.logger.debug(`原始数据: ${JSON.stringify(data)}`);

    const mappings = fieldMappings[table as TableName];
    if (!mappings) {
      this.logger.debug(`表 ${table} 没有定义字段映射，返回原始数据`);
      return data;
    }

    const reverseMappings = Object.entries(mappings).reduce((acc, [key, value]) => {
      acc[value] = key;
      return acc;
    }, {} as Record<string, string>);

    const result = Object.entries(data).reduce((acc, [key, value]) => {
      const entityField = reverseMappings[key] || key;
      acc[entityField] = value;
      this.logger.debug(`字段映射: ${key} -> ${entityField}`);
      return acc;
    }, {} as Record<string, any>);

    return result;
  }
} 