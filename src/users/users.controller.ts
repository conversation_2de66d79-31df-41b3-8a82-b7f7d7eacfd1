import { Controller, Get, Post, Body, Param, UseGuards } from '@nestjs/common';
import { UsersService } from './users.service';
import { User } from './users.entity';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';

@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  async findAll(): Promise<User[]> {
    return this.usersService.findAll();
  }

  @Post()
  @UseGuards(JwtAuthGuard)
  async create(@Body() body: { name: string; email: string }): Promise<User> {
    return this.usersService.create(body.name, body.email);
  }

  // 发送手机验证码
  @Post('phone/send-code')
  async sendVerificationCode(@Body('phoneNumber') phoneNumber: string) {
    return this.usersService.sendVerificationCode(phoneNumber);
  }

  // 验证手机验证码
  @Post('phone/verify-code')
  async verifyPhoneCode(
    @Body('phoneNumber') phoneNumber: string,
    @Body('code') code: string,
  ) {
    return this.usersService.verifyPhoneCode(phoneNumber, code);
  }

  // 手机号注册
  @Post('phone/register')
  async registerWithPhone(
    @Body('phoneNumber') phoneNumber: string,
    @Body('name') name: string,
    @Body('password') password: string,
  ) {
    return this.usersService.registerWithPhone(phoneNumber, name, password);
  }

  // 手机号登录
  @Post('phone/login')
  async loginWithPhone(
    @Body('phoneNumber') phoneNumber: string,
    @Body('password') password: string,
  ) {
    return this.usersService.loginWithPhone(phoneNumber, password);
  }

  // 小程序登录
  @Post('wechat/login')
  async loginWithWechat(@Body('code') code: string) {
    console.log('code', code);
    return this.usersService.loginWithWechat(code);
  }

  // 管理员账号密码登录
  @Post('admin/login')
  async loginWithAccount(
    @Body('username') username: string,
    @Body('password') password: string,
  ) {
    return this.usersService.loginWithAccount(username, password);
  }

  // 创建管理员账号
  @Post('admin/create')
  async createAdminAccount(
    @Body('username') username: string,
    @Body('password') password: string,
    @Body('name') name: string,
  ) {
    return this.usersService.createAdminAccount(username, password, name);
  }
}