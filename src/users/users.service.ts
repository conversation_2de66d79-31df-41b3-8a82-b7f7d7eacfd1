import { Injectable, Inject, BadRequestException, NotFoundException } from '@nestjs/common';
import { User } from './users.entity';
import { DatabaseClient, DATABASE_CLIENT } from '../interfaces/database.interface';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import axios from 'axios';

@Injectable()
export class UsersService {
  constructor(
    @Inject(DATABASE_CLIENT)
    private readonly databaseClient: DatabaseClient,
    private jwtService: JwtService,
  ) {}

  // 生成验证码
  private generateVerificationCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  // 发送验证码
  async sendVerificationCode(phoneNumber: string) {
    // 检查是否存在未过期的验证码
    const existingVerifications = await this.databaseClient.query('phone_verifications', {
      where: {
        phoneNumber,
        isVerified: false,
        expiresAt: { $gt: new Date() }
      }
    });

    if (existingVerifications.length > 0) {
      throw new BadRequestException('请等待之前的验证码过期后再试');
    }

    const verificationCode = this.generateVerificationCode();
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + 5); // 验证码5分钟有效

    const verification = {
      phoneNumber,
      verificationCode,
      isVerified: false,
      expiresAt,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await this.databaseClient.insert('phone_verifications', verification);

    // TODO: 集成短信服务发送验证码
    console.log(`验证码: ${verificationCode}`);

    return { message: '验证码已发送' };
  }

  // 验证手机验证码
  async verifyPhoneCode(phoneNumber: string, code: string) {
    const verifications = await this.databaseClient.query('phone_verifications', {
      where: {
        phoneNumber,
        verificationCode: code,
        isVerified: false,
        expiresAt: { $gt: new Date() }
      }
    });

    if (verifications.length === 0) {
      throw new BadRequestException('验证码无效或已过期');
    }

    const verification = verifications[0];
    await this.databaseClient.update('phone_verifications', verification.id, {
      isVerified: true,
      updatedAt: new Date()
    });

    return { message: '验证成功' };
  }

  // 手机号注册
  async registerWithPhone(phoneNumber: string, name: string, password: string) {
    // 检查手机号是否已被注册
    const existingUsers = await this.databaseClient.query('users', {
      where: { phoneNumber }
    });

    if (existingUsers.length > 0) {
      throw new BadRequestException('该手机号已被注册');
    }

    // 检查验证码是否已验证
    const verifications = await this.databaseClient.query('phone_verifications', {
      where: {
        phoneNumber,
        isVerified: true,
        expiresAt: { $gt: new Date() }
      }
    });

    if (verifications.length === 0) {
      throw new BadRequestException('请先完成手机号验证');
    }

    const hashedPassword = await bcrypt.hash(password, 10);
    const userData = {
      phoneNumber,
      name,
      password: hashedPassword,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const createdUser = await this.databaseClient.insert<User>('users', userData);

    // 生成 JWT token
    const token = this.jwtService.sign({ 
      sub: createdUser.id,
      phoneNumber: createdUser.phoneNumber 
    });

    return { token };
  }

  // 手机号登录
  async loginWithPhone(phoneNumber: string, password: string) {
    const users = await this.databaseClient.query<User>('users', {
      where: { phoneNumber }
    });

    if (users.length === 0) {
      throw new NotFoundException('用户不存在');
    }

    const user = users[0];
    
    // 检查用户是否有密码
    if (!user.password) {
      throw new BadRequestException('该账号未设置密码，请先设置密码');
    }

    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new BadRequestException('密码错误');
    }

    const token = this.jwtService.sign({ 
      sub: user.id,
      phoneNumber: user.phoneNumber 
    });

    return { token };
  }

  // 小程序登录
  async loginWithWechat(code: string) {
    console.log('[WechatLogin] 开始微信登录流程，code:', code);
    
    // 配置微信小程序信息
    const appId = process.env.WECHAT_APP_ID;
    const appSecret = process.env.WECHAT_APP_SECRET;
    
    console.log('[WechatLogin] 环境变量检查:', { 
      hasAppId: !!appId, 
      hasAppSecret: !!appSecret 
    });
    
    if (!appId || !appSecret) {
      console.error('[WechatLogin] 微信小程序配置错误:', { appId, appSecret });
      throw new BadRequestException('微信小程序配置错误');
    }

    try {
      console.log('[WechatLogin] 准备调用微信接口获取 openid');
      // 调用微信接口获取 openid
      const response = await axios.get('https://api.weixin.qq.com/sns/jscode2session', {
        params: {
          appid: appId,
          secret: appSecret,
          js_code: code,
          grant_type: 'authorization_code'
        }
      });

      console.log('[WechatLogin] 微信接口返回数据:', response.data);

      if (response.data.errcode) {
        console.error('[WechatLogin] 微信接口返回错误:', response.data);
        // 如果是 code 已被使用，返回特定错误信息
        if (response.data.errcode === 40029) {
          throw new BadRequestException('登录已过期，请重新登录');
        }
        throw new BadRequestException('微信登录失败：' + response.data.errmsg);
      }

      const { openid, session_key } = response.data;
      console.log('[WechatLogin] 成功获取 openid:', openid);

      // 查找或创建用户
      console.log('[WechatLogin] 开始查询用户信息');
      let users = await this.databaseClient.query<User>('users', {
        where: { openid }
      });

      console.log('[WechatLogin] 用户查询结果:', { 
        found: users.length > 0, 
        userId: users[0]?.id 
      });

      let user: User;
      if (users.length === 0) {
        console.log('[WechatLogin] 用户不存在，开始创建新用户');
        // 创建新用户
        user = await this.databaseClient.insert<User>('users', {
          openid,
          created_at: new Date(),
          updated_at: new Date()
        });
        console.log('[WechatLogin] 新用户创建成功:', { userId: user.id });
      } else {
        user = users[0];
        console.log('[WechatLogin] 使用已存在的用户:', { userId: user.id });
      }

      // 生成 JWT token
      console.log('[WechatLogin] 开始生成 JWT token');
      const token = this.jwtService.sign({ 
        sub: user.id,
        openid: user.openid 
      });
      console.log('[WechatLogin] JWT token 生成成功');

      return { 
        token,
        user: {
          id: user.id,
          openid: user.openid,
          name: user.name,
          avatar: user.avatar
        }
      };
    } catch (error) {
      console.error('[WechatLogin] 发生错误:', {
        error: error.message,
        stack: error.stack,
        response: error.response?.data
      });
      
      if (error.response?.data?.errcode === 40029) {
        throw new BadRequestException('登录已过期，请重新登录');
      }
      throw error;
    }
  }

  // 管理员账号密码登录
  async loginWithAccount(username: string, password: string) {
    const users = await this.databaseClient.query<User>('users', {
      where: { username }
    });

    if (users.length === 0) {
      throw new NotFoundException('用户不存在');
    }

    const user = users[0];
    
    // 检查用户是否有密码
    if (!user.password) {
      throw new BadRequestException('该账号未设置密码');
    }

    // 检查用户是否是管理员
    if (!user.is_admin) {
      throw new BadRequestException('该账号不是管理员账号');
    }


    // 直接比较密码
    if (password !== user.password) {
      throw new BadRequestException('密码错误');
    }

    const token = this.jwtService.sign({ 
      sub: user.id,
      username: user.username,
      isAdmin: user.is_admin
    });

    return { 
      token,
      user: {
        id: user.id,
        username: user.username,
        name: user.name
      }
    };
  }

  // 创建管理员账号
  async createAdminAccount(username: string, password: string, name: string) {
    // 检查用户名是否已存在
    const existingUsers = await this.databaseClient.query<User>('users', {
      where: { username }
    });

    if (existingUsers.length > 0) {
      throw new BadRequestException('该用户名已被使用');
    }

    const userData = {
      username,
      password,
      name,
      is_admin: true,
      created_at: new Date(),
      updated_at: new Date()
    };

    return this.databaseClient.insert<User>('users', userData);
  }

  // 原有的方法保持不变
  async findAll(): Promise<User[]> {
    return this.databaseClient.query<User>('users');
  }

  async create(name: string, email: string): Promise<User> {
    return this.databaseClient.insert<User>('users', { name, email });
  }

  async findOne(id: string): Promise<User | null> {
    const users = await this.databaseClient.query<User>('users', {
      where: { id },
      limit: 1,
    });
    return users[0] || null;
  }

  async update(id: string, data: Partial<User>): Promise<User> {
    return this.databaseClient.update<User>('users', id, data);
  }

  async remove(id: string): Promise<void> {
    await this.databaseClient.delete<User>('users', id);
  }
}