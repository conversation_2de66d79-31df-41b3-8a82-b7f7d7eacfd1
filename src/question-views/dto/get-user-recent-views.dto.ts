import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { QuestionView } from '../question-view.entity';

export class GetUserRecentViewsDto {
  @ApiProperty({ description: '页码', example: 1 })
  page: number;

  @ApiProperty({ description: '每页数量', example: 10 })
  limit: number;
}

export class UserRecentViewItemDto {
  @ApiProperty({ description: '浏览记录ID' })
  id: string;

  @ApiProperty({ description: '面试题ID' })
  questionId: string;

  @ApiProperty({ description: '浏览开始时间' })
  viewedAt: Date;

  @ApiProperty({ description: '浏览时长（单位：秒）' })
  duration: number;

  @ApiPropertyOptional({ description: '问题标题' })
  title?: string;

  @ApiPropertyOptional({ description: '关键点', type: [String] })
  keyPoints?: string[];

  @ApiPropertyOptional({ description: '技巧提示' })
  tips?: string;

  @ApiPropertyOptional({ description: '公司ID' })
  companyId?: number;

  @ApiPropertyOptional({ description: '公司名称' })
  companyName?: string;

  @ApiPropertyOptional({ description: '分类', type: [String] })
  category?: string[];

  @ApiPropertyOptional({ description: '产品经理类型ID' })
  pmTypeId?: number;

  @ApiPropertyOptional({ description: '产品经理类型名称' })
  pmTypeName?: string;

  @ApiPropertyOptional({ description: '创建时间' })
  createdAt?: string;

  @ApiPropertyOptional({ description: '更新时间' })
  updatedAt?: string;
}

export class UserRecentViewsResultDto {
  @ApiProperty({ description: '总记录数' })
  total: number;

  @ApiProperty({ description: '当前页' })
  page: number;

  @ApiProperty({ description: '每页数量' })
  limit: number;

  @ApiProperty({ type: [UserRecentViewItemDto], description: '浏览记录列表' })
  records: UserRecentViewItemDto[];
} 