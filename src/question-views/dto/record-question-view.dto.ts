import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsUUID, IsDateString, Min } from 'class-validator';

export class RecordQuestionViewDto {
  @IsNotEmpty({ message: '面试题ID不能为空' })
  @IsUUID('4', { message: '面试题ID格式不正确' })
  @ApiProperty({ description: '面试题ID', example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef' })
  questionId: string;

  @IsNotEmpty({ message: '浏览开始时间不能为空' })
  @IsDateString({}, { message: '浏览开始时间格式不正确' })
  @ApiProperty({ description: '浏览开始时间', example: '2023-10-27T10:00:00.000Z' })
  viewedAt: string; // 使用 ISO 8601 格式的字符串

  @IsNotEmpty({ message: '浏览时长不能为空' })
  @IsNumber({}, { message: '浏览时长必须是数字' })
  @Min(0, { message: '浏览时长不能为负数' })
  @ApiProperty({ description: '浏览时长（单位：秒）', example: 60 })
  duration: number;
} 