import { Injectable, Inject } from '@nestjs/common';
import { DatabaseService } from '../databases/database.service';
import { FindOptionsWhere, ObjectLiteral } from '../interfaces/database.interface';
import { QuestionView } from './question-view.entity';
import { RecordQuestionViewDto } from './dto/record-question-view.dto';
import { Logger } from '@nestjs/common';
import { isUUID } from 'class-validator';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { Question } from '../questions/question.entity';
import { In } from 'typeorm';

@Injectable()
export class QuestionViewsService {
  private readonly tableName = 'question_views';
  private readonly logger = new Logger(QuestionViewsService.name);

  constructor(private readonly dbService: DatabaseService) {}

  /**
   * 记录一次面试题浏览
   * @param userId - 用户ID
   * @param recordDto - 浏览记录数据传输对象
   * @returns 创建的浏览记录实体
   */
  async recordView(userId: string | number, recordDto: RecordQuestionViewDto): Promise<Partial<QuestionView>> {
    const { questionId, viewedAt, duration } = recordDto;

    // 验证 questionId 的 UUID 格式
    if (!isUUID(questionId, '4')) {
      this.logger.error(`Invalid questionId format: ${questionId}`);
      throw new NotFoundException(`无效的问题ID格式: ${questionId}`);
    }

    // 验证问题是否存在
    try {
      const question = await this.dbService.findOne<Question>('questions', { where: { id: recordDto.questionId } as FindOptionsWhere<Question> });
      if (!question) {
        this.logger.error(`Question not found: ${recordDto.questionId}`);
        throw new NotFoundException(`未找到ID为 ${recordDto.questionId} 的问题`);
      }
    } catch (error) {
      this.logger.error('Error checking question existence:', error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new NotFoundException('验证问题存在性时发生错误');
    }

    const numericUserId = typeof userId === 'string' ? parseInt(userId, 10) : userId;
    if (isNaN(numericUserId)) {
      throw new BadRequestException('无效的用户ID'); // Add check for NaN
    }

    // 准备要插入的数据
    const newViewData = {
      user_id: numericUserId,
      question_id: questionId,
      viewed_at: viewedAt || new Date(),
      duration: duration || 0,
    };

    this.logger.debug('Inserting view record:', newViewData);

    try {
      // 使用数据库服务创建记录
      const createdView = await this.dbService.create(this.tableName, newViewData);
      this.logger.debug('View record created:', createdView);
      
      // Map the ObjectLiteral to Partial<QuestionView>
      // Ensure properties from createdView (DB columns) are mapped to QuestionView entity properties
      const mappedView: Partial<QuestionView> = {
        id: createdView['id'], // Assuming 'id' is returned from create
        userId: String(createdView['user_id']), // Ensure string type for userId
        questionId: createdView['question_id'],
        viewedAt: new Date(createdView['viewed_at']), // Ensure Date type
        duration: createdView['duration'],
        createdAt: new Date(createdView['created_at']), // Ensure Date type
        updatedAt: new Date(createdView['updated_at'])  // Ensure Date type
      };
      return mappedView;
    } catch (error) {
      this.logger.error('Failed to create view record:', error);
      throw error;
    }
  }

  /**
   * 查询用户最近浏览记录，支持分页
   * @param userId 用户ID
   * @param page 页码
   * @param limit 每页数量
   * @returns 用户最近浏览记录及总数
   */
  async findUserRecentViews(userId: string, page: number, limit: number): Promise<{ total: number; records: Partial<QuestionView>[] }> {
    const offset = (page - 1) * limit;
    const total = await this.dbService.count(this.tableName, { user_id: userId });

    // 判断当前数据库类型
    const dbType = process.env.DB_TYPE || 'supabase';
    let rawRecords: any[] = [];
    if (dbType === 'supabase') {
      // Supabase 只 join questions，嵌套 select company/pm_type
      rawRecords = await this.dbService.find<any>(this.tableName, {
        where: { user_id: userId },
        limit,
        offset,
        orderBy: { viewed_at: 'desc' },
        join: [
          {
            table: 'questions',
            on: { 'question_views.question_id': 'questions.id' },
            select: [
              'id', 'title', 'keyPoints', 'tips', 'category', 'createdAt', 'updatedAt', 'company_id', 'pm_type_id', 'answer', 'content',
              'company(name)', 'pm_type(name)'
            ]
          }
        ]
      });
    } else {
      // TypeORM 多表 join
      rawRecords = await this.dbService.find<any>(this.tableName, {
        where: { user_id: userId },
        limit,
        offset,
        orderBy: { viewed_at: 'desc' },
        join: [
          {
            table: 'questions',
            on: { 'question_views.question_id': 'questions.id' },
            select: ['id', 'title', 'keyPoints', 'tips', 'category', 'createdAt', 'updatedAt', 'company_id', 'pm_type_id', 'answer', 'content']
          },
          {
            table: 'company',
            on: { 'questions.company_id': 'company.id' },
            select: ['name']
          },
          {
            table: 'pm_type',
            on: { 'questions.pm_type_id': 'pm_type.id' },
            select: ['name']
          }
        ]
      });
    }

    if (!rawRecords || rawRecords.length === 0) {
      return { total, records: [] };
    }

    const mappedRecords: Partial<QuestionView>[] = rawRecords.map(record => {
      // 解析题目信息
      let question: Partial<Question> | undefined = undefined;
      let title = '';
      let keyPoints: string[] = [];
      let tips = undefined;
      let category = [];
      let createdAt = undefined;
      let updatedAt = undefined;
      let companyId = undefined;
      let pmTypeId = undefined;
      let companyName = '';
      let pmTypeName = '';
      let answer = undefined;
      let content = undefined;
      if (dbType === 'supabase') {
        // Supabase: questions: { title, company: { name }, pm_type: { name } }
        if (record['questions'] && record['questions'].title) title = record['questions'].title;
        if (record['questions'] && record['questions'].keyPoints) keyPoints = record['questions'].keyPoints;
        if (record['questions'] && record['questions'].tips) tips = record['questions'].tips;
        if (record['questions'] && record['questions'].category) category = record['questions'].category;
        if (record['questions'] && record['questions'].createdAt) createdAt = record['questions'].createdAt;
        if (record['questions'] && record['questions'].updatedAt) updatedAt = record['questions'].updatedAt;
        if (record['questions'] && record['questions'].companyId) companyId = record['questions'].companyId;
        if (record['questions'] && record['questions'].pmTypeId) pmTypeId = record['questions'].pmTypeId;
        if (record['questions'] && record['questions'].company && record['questions'].company.name) companyName = record['questions'].company.name;
        if (record['questions'] && record['questions'].pm_type && record['questions'].pm_type.name) pmTypeName = record['questions'].pm_type.name;
        if (record['questions'] && record['questions'].answer) answer = record['questions'].answer;
        if (record['questions'] && record['questions'].content) content = record['questions'].content;
      } else {
        // TypeORM: questions_title, company_name, pm_type_name
        if (record['questions_title']) title = record['questions_title'];
        if (record['questions_keyPoints']) keyPoints = record['questions_keyPoints'];
        if (record['questions_tips']) tips = record['questions_tips'];
        if (record['questions_category']) category = record['questions_category'];
        if (record['questions_createdAt']) createdAt = record['questions_createdAt'];
        if (record['questions_updatedAt']) updatedAt = record['questions_updatedAt'];
        if (record['questions_company_id']) companyId = record['questions_company_id'];
        if (record['questions_pm_type_id']) pmTypeId = record['questions_pm_type_id'];
        if (record['company_name']) companyName = record['company_name'];
        if (record['pm_type_name']) pmTypeName = record['pm_type_name'];
        if (record['questions_answer']) answer = record['questions_answer'];
        if (record['questions_content']) content = record['questions_content'];
      }
      const questionIdStr = dbType === 'supabase'
        ? String((record['questions'] && record['questions'].id) || '')
        : String(record['questions_id'] || '');
      if (!questionIdStr) {
        throw new Error('question.id 不能为空，数据异常');
      }
      let questionObj: Question | undefined = undefined;
      if (questionIdStr) {
        questionObj = {
          id: questionIdStr,
          title: title || '',
          content: content || '',
          keyPoints: keyPoints || [],
          answer: answer || '',
          tips: tips ?? null,
          companyId: companyId ?? null,
          companyName: companyName || null,
          category: category || [],
          pmTypeId: pmTypeId ?? null,
          pmTypeName: pmTypeName || null,
          createdAt: createdAt || '',
          updatedAt: updatedAt || ''
        };
      }
      const qv: Partial<QuestionView> = {
        id: record['id'],
        userId: String(record['user_id']),
        questionId: record['question_id'],
        viewedAt: new Date(record['viewed_at']),
        duration: record['duration'],
        createdAt: new Date(record['created_at']),
        updatedAt: new Date(record['updated_at']),
        question: questionObj
      };
      return qv;
    });
    return { total, records: mappedRecords };
  }

  // 如果需要，可以在这里添加其他查询方法，例如：
  // async findViewsByUser(userId: string): Promise<QuestionView[]> { ... }
  // async findViewsByQuestion(questionId: string): Promise<QuestionView[]> { ... }
} 