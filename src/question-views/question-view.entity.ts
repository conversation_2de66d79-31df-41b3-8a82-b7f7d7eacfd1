import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index, ManyToOne, JoinColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Question } from '../questions/question.entity';

@Entity('question_views') // 指定数据库表名
export class QuestionView {
  @PrimaryGeneratedColumn('uuid') // 使用 UUID 作为主键
  @ApiProperty({ description: '浏览记录ID' })
  id: string;

  @Column({ type: 'uuid', comment: '用户ID' })
  @Index() // 为 user_id 添加索引以优化查询
  @ApiProperty({ description: '用户ID' })
  userId: string;

  @Column({ type: 'uuid', comment: '面试题ID' })
  @Index() // 为 question_id 添加索引
  @ApiProperty({ description: '面试题ID' })
  questionId: string;

  @Column({ type: 'timestamp', comment: '浏览开始时间' })
  @ApiProperty({ description: '浏览开始时间' })
  viewedAt: Date;

  @Column({ type: 'int', comment: '浏览时长（单位：秒）' })
  @ApiProperty({ description: '浏览时长（单位：秒）' })
  duration: number; // 假设单位为秒

  @CreateDateColumn({ comment: '创建时间' })
  @ApiProperty({ description: '记录创建时间' })
  createdAt: Date;

  @UpdateDateColumn({ comment: '更新时间' })
  @ApiProperty({ description: '记录更新时间' })
  updatedAt: Date;

  @ManyToOne(() => Question)
  @JoinColumn({ name: 'question_id' })
  question?: Question; // 允许 question 为 undefined
} 