import { Controller, Post, Body, UseGuards, Request, HttpCode, HttpStatus, UnauthorizedException, BadRequestException, Logger, NotFoundException, Get, Query, Param, ParseIntPipe, DefaultValuePipe } from '@nestjs/common';
import { QuestionViewsService } from './question-views.service';
import { RecordQuestionViewDto } from './dto/record-question-view.dto';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery, ApiParam } from '@nestjs/swagger';
import { QuestionView } from './question-view.entity';
import { ValidationPipe } from '@nestjs/common';
import { GetUserRecentViewsDto, UserRecentViewsResultDto, UserRecentViewItemDto } from './dto/get-user-recent-views.dto';
import { User } from '../decorators/user.decorator';
import { UserPayload } from '../auth/user-payload.interface';

@ApiTags('question-views') // Swagger UI 中的标签
@ApiBearerAuth() // 表明此 Controller 的接口需要 JWT 认证
@Controller('question-views')
export class QuestionViewsController {
  private readonly logger = new Logger(QuestionViewsController.name);

  constructor(private readonly questionViewsService: QuestionViewsService) {}

  @Post()
  @UseGuards(JwtAuthGuard) // 使用 JWT 守卫进行权限验证
  @HttpCode(HttpStatus.CREATED) // 设置成功响应状态码为 201
  @ApiOperation({ summary: '记录面试题浏览' })
  @ApiResponse({ status: 201, description: '成功记录浏览', type: QuestionView }) // 指定成功响应类型
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 401, description: '未授权' })
  @ApiResponse({ status: 404, description: '问题不存在' })
  async recordQuestionView(
    @User() user: UserPayload,
    @Body(new ValidationPipe({ transform: true, whitelist: true })) recordQuestionViewDto: RecordQuestionViewDto, // 验证请求体
  ): Promise<Partial<QuestionView>> {
    
    try {
      // 直接使用用户 ID
      return await this.questionViewsService.recordView(user.id, recordQuestionViewDto);
    } catch (error) {
      
      // 处理 NotFoundException
      if (error instanceof NotFoundException) {
        throw error;
      }
      
      // 处理其他错误
      if (error.response?.message) {
        throw new BadRequestException({
          message: '请求参数验证失败',
          errors: error.response.message,
        });
      }
      
      throw error;
    }
  }

  @Get('user/recent')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '查询当前用户最近浏览记录（分页）' })
  @ApiResponse({ status: 200, description: '用户最近浏览记录', type: UserRecentViewsResultDto })
  async getUserRecentViews(
    @Request() req,
    @Query() query: GetUserRecentViewsDto,
  ): Promise<UserRecentViewsResultDto> {
    const user = req.user;
    if (!user || !user.id) {
      this.logger.error('User not found in request:', req.user);
      throw new UnauthorizedException('无法识别用户，请确认 JWT 令牌有效');
    }
    const page = Number(query.page) > 0 ? Number(query.page) : 1;
    const limit = Number(query.limit) > 0 ? Number(query.limit) : 10;
    const { total, records } = await this.questionViewsService.findUserRecentViews(user.id, page, limit);
    return {
      total,
      page,
      limit,
      records: records.map(item => ({
        id: item.id ?? '',
        questionId: item.question?.id ?? item.questionId ?? '',
        viewedAt: item.viewedAt ?? new Date(),
        duration: item.duration ?? 0,
        title: item.question?.title,
        keyPoints: item.question?.keyPoints,
        tips: item.question?.tips === null ? undefined : item.question?.tips,
        companyId: item.question?.companyId === null ? undefined : item.question?.companyId,
        companyName: item.question?.companyName === null ? undefined : item.question?.companyName,
        category: item.question?.category,
        pmTypeId: item.question?.pmTypeId === null ? undefined : item.question?.pmTypeId,
        pmTypeName: item.question?.pmTypeName === null ? undefined : item.question?.pmTypeName,
        createdAt: item.question?.createdAt,
        updatedAt: item.question?.updatedAt,
      })),
    };
  }

  @Get('recent')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取用户最近浏览记录（分页）' })
  @ApiQuery({ name: 'page', required: false, description: '页码', type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量', type: Number, example: 10 })
  async findUserRecentViews(
    @User() user: UserPayload,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
  ): Promise<UserRecentViewsResultDto> {
    const result = await this.questionViewsService.findUserRecentViews(user.id, page, limit);

    const mappedRecords: UserRecentViewItemDto[] = [];
    for (const record of result.records) {
      if (
        typeof record.id === 'string' &&         // Explicit type check for string
        typeof record.questionId === 'string' && // Explicit type check for string
        record.viewedAt instanceof Date &&       // Explicit type check for Date
        typeof record.duration === 'number'     // Explicit type check for number
      ) {
        // All required fields are now confirmed to be of the correct non-null/non-undefined type
        const id: string = record.id;
        const questionId: string = record.questionId;
        const viewedAt: Date = record.viewedAt;
        const duration: number = record.duration;

        const item: UserRecentViewItemDto = {
          id: id, 
          questionId: questionId, 
          viewedAt: viewedAt, 
          duration: duration, 
          title: record.question?.title ?? undefined,
          keyPoints: record.question?.keyPoints ?? undefined,
          tips: record.question?.tips === null ? undefined : record.question?.tips,
          companyId: record.question?.companyId === null ? undefined : record.question?.companyId,
          companyName: record.question?.companyName === null ? undefined : record.question?.companyName,
          category: record.question?.category ?? undefined,
          pmTypeId: record.question?.pmTypeId === null ? undefined : record.question?.pmTypeId,
          pmTypeName: record.question?.pmTypeName === null ? undefined : record.question?.pmTypeName,
          createdAt: record.createdAt ? new Date(record.createdAt).toISOString() : undefined,
          updatedAt: record.updatedAt ? new Date(record.updatedAt).toISOString() : undefined,
        };
        mappedRecords.push(item);
      } else {
        this.logger.warn('Skipping incomplete view record during mapping due to missing or mistyped essential fields:', record);
      }
    }

    return {
      total: result.total, 
      page: page,
      limit: limit,
      records: mappedRecords,
    };
  }

  @Get('question/:questionId/stats')
  @ApiOperation({ summary: '获取问题的浏览统计信息' })
  @ApiParam({ name: 'questionId', description: '问题ID', type: String })
  async getQuestionViewStats(@Param('questionId') questionId: string) {
    return { message: 'Stats endpoint not implemented yet.', questionId };
  }
} 