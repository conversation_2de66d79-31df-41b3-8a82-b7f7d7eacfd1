import { Module } from '@nestjs/common';
import { QuestionViewsService } from './question-views.service';
import { QuestionViewsController } from './question-views.controller';
import { DatabaseModule } from '../databases/database.module';
// 如果 QuestionView 实体需要在 TypeOrmModule 中注册，也需要导入 TypeOrmModule
// import { TypeOrmModule } from '@nestjs/typeorm';
// import { QuestionView } from './question-view.entity';

@Module({
  imports: [
    DatabaseModule, // 导入 DatabaseModule
    // 如果使用 TypeORM 并且希望在这里管理实体
    // TypeOrmModule.forFeature([QuestionView]),
  ],
  controllers: [QuestionViewsController],
  providers: [QuestionViewsService],
  // 如果其他模块需要使用 QuestionViewsService，可以在这里导出
  // exports: [QuestionViewsService],
})
export class QuestionViewsModule {} 