import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import { CreateQuestionDto } from './create-question.dto';
import { UpdateQuestionDto } from './update-question.dto';
import { Question } from './question.entity';
import { DatabaseService } from '../databases/database.service';
import { DATABASE_CLIENT, FindOptionsWhere, ObjectLiteral, DatabaseClient } from '../interfaces/database.interface';
import { SearchQuestionDto } from './search-question.dto';
import { Company } from '../company/company.entity';
import { PmType } from '../pm-type/pm-type.entity';
import { FieldMapper } from '../util/field-mapper.util';

interface CountItem {
  name: string;
  count: number;
}

interface FindAllParams {
  companyId?: number;
  category?: string;
  search?: string;
  pmTypeId?: number;
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
}

interface FindAllResponse {
  data: Question[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

@Injectable()
export class QuestionsService {
  constructor(
    private readonly databaseService: DatabaseService,
    @Inject(DATABASE_CLIENT)
    private readonly databaseClient: DatabaseClient,
  ) {}

  async create(createQuestionDto: CreateQuestionDto): Promise<Question> {
    const now = new Date();
    const questionData = {
      ...createQuestionDto,
      company_id: createQuestionDto.companyId,
      pm_type_id: createQuestionDto.pmTypeId,
      createdAt: now,
      updatedAt: now,
    };
    if (typeof createQuestionDto.keyPoints === 'string') {
      questionData.keyPoints = [createQuestionDto.keyPoints];
    }
    if (typeof createQuestionDto.category === 'string') {
        questionData.category = [createQuestionDto.category];
    }

    const result = await this.databaseService.create('questions', questionData as ObjectLiteral);
    return this.mapToQuestion(result);
  }

  async findAll(params: FindAllParams = {}): Promise<FindAllResponse> {
    const methodStart = Date.now();
    console.log('[QuestionsService.findAll] 方法入口', methodStart);

    const { companyId, category, search, pmTypeId, page = 1, limit = 10, sort = 'createdAt', order = 'desc' } = params;
    const where: FindOptionsWhere<ObjectLiteral> = {};
    if (companyId) where['company_id'] = companyId;
    if (category) where['category'] = category;
    if (pmTypeId) where['pm_type_id'] = pmTypeId;
    if (search) {
      where['title'] = { like: `%${search}%` };
    }

    const offset = (page - 1) * limit;
    const orderBy = { [sort]: order };

    const beforeDb = Date.now();
    console.log('[QuestionsService.findAll] 数据库查询前', beforeDb, '距离入口', beforeDb - methodStart, 'ms');

    const countPromise = this.databaseService.count('questions', where as any);
    const queryPromise = this.databaseClient.query<any>('questions', {
      where,
      limit,
      offset,
      order: { column: sort, ascending: order === 'asc' },
      join: [
        {
          table: 'company',
          on: { 'questions.company_id': 'company.id' },
          select: ['name']
        },
        {
          table: 'pm_type',
          on: { 'questions.pm_type_id': 'pm_type.id' },
          select: ['name']
        }
      ]
    });
    const [total, data] = await Promise.all([countPromise, queryPromise]);

    const afterDb = Date.now();
    console.log('[QuestionsService.findAll] 数据库查询后', afterDb, '耗时', afterDb - beforeDb, 'ms');

    const transformStart = Date.now();
    console.log('[QuestionsService.findAll] 数据转换前', transformStart, '距离数据库查询后', transformStart - afterDb, 'ms');
    const transformedData = data.map(item => {
      let companyName = '';
      let pmTypeName = '';
      if (item['company_name']) companyName = item['company_name'];
      else if (item['company'] && item['company'].name) companyName = item['company'].name;
      if (item['pm_type_name']) pmTypeName = item['pm_type_name'];
      else if (item['pm_type'] && item['pm_type'].name) pmTypeName = item['pm_type'].name;
      return {
        ...this.mapToQuestion(item),
        companyName,
        pmTypeName
      };
    });
    const transformEnd = Date.now();
    console.log('[QuestionsService.findAll] 数据转换后', transformEnd, '耗时', transformEnd - transformStart, 'ms');

    const methodEnd = Date.now();
    console.log('[QuestionsService.findAll] 方法出口', methodEnd, '总耗时', methodEnd - methodStart, 'ms');

    return {
      data: transformedData,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  async findOne(id: string): Promise<Question> {
    const resultArr = await this.databaseClient.query<any>('questions', {
      where: { id },
      join: [
        {
          table: 'company',
          on: { 'questions.company_id': 'company.id' },
          select: ['name']
        },
        {
          table: 'pm_type',
          on: { 'questions.pm_type_id': 'pm_type.id' },
          select: ['name']
        }
      ]
    });
    const item = resultArr && resultArr[0];
    if (!item) {
      throw new NotFoundException(`问题 ID '${id}' 未找到`);
    }
    let companyName = '';
    let pmTypeName = '';
    if (item['company_name']) companyName = item['company_name'];
    else if (item['company'] && item['company'].name) companyName = item['company'].name;
    if (item['pm_type_name']) pmTypeName = item['pm_type_name'];
    else if (item['pm_type'] && item['pm_type'].name) pmTypeName = item['pm_type'].name;
    return {
      ...this.mapToQuestion(item),
      companyName,
      pmTypeName
    };
  }

  async update(id: string, updateQuestionDto: UpdateQuestionDto): Promise<Question> {
    const now = new Date();
    const questionDataToUpdate = {
      ...updateQuestionDto,
      updatedAt: now,
    } as ObjectLiteral;
    
    if (typeof updateQuestionDto.keyPoints === 'string') {
        (questionDataToUpdate as any).keyPoints = [updateQuestionDto.keyPoints];
    }
    if (typeof updateQuestionDto.category === 'string') {
        (questionDataToUpdate as any).category = [updateQuestionDto.category];
    }

    // 使用 FieldMapper 转换字段名
    const mappedData = FieldMapper.toDatabase('questions', questionDataToUpdate);

    const existingRecord = await this.databaseService.findOne('questions', { where: { id: id } as FindOptionsWhere<ObjectLiteral> });
    if (!existingRecord) {
        throw new NotFoundException(`问题 ID '${id}' 未找到，无法更新`);
    }

    await this.databaseService.update('questions', id, mappedData);
    
    const updatedRecord = await this.databaseService.findOne('questions', { where: { id: id } as FindOptionsWhere<ObjectLiteral> });
    if (!updatedRecord) {
        throw new NotFoundException(`更新后未能重新获取问题 ID '${id}'`);
    }
    return this.mapToQuestion(updatedRecord);
  }

  async remove(id: string): Promise<void> {
    const questionExists = await this.databaseService.findOne('questions', { where: { id: id } as FindOptionsWhere<ObjectLiteral> });
    if (!questionExists) {
      throw new NotFoundException(`问题 ID '${id}' 未找到，无法删除`);
    }
    await this.databaseService.delete('questions', { where: { id: id } as FindOptionsWhere<ObjectLiteral> });
  }

  async getDistinctCompanies(): Promise<CountItem[]> {
    try {
      console.log('Fetching distinct companies...');
      const result = await this.databaseClient.query<{ company_id: number }>('questions', {
        select: 'company_id',
        where: { company_id: { neq: null } }
      });

      const countMap = result.reduce((acc, item) => {
        if (item.company_id) {
          acc[item.company_id] = (acc[item.company_id] || 0) + 1;
        }
        return acc;
      }, {} as Record<number, number>);

      const items: CountItem[] = Object.entries(countMap).map(([name, count]) => ({
        name: name,
        count: Number(count)
      }));
      return items.sort((a, b) => b.count - a.count);
    } catch (error) {
      console.error('Error in getDistinctCompanies:', error);
      throw error;
    }
  }

  async getDistinctPmTypes(): Promise<CountItem[]> {
    try {
      console.log('Fetching distinct PM types...');
      const result = await this.databaseClient.query<{ pm_type_id: number }>('questions', {
        select: 'pm_type_id',
        where: { pm_type_id: { neq: null } }
      });

      const countMap = result.reduce((acc, item) => {
        if (item.pm_type_id) {
          acc[item.pm_type_id] = (acc[item.pm_type_id] || 0) + 1;
        }
        return acc;
      }, {} as Record<number, number>);

      const items: CountItem[] = Object.entries(countMap).map(([name, count]) => ({
        name: name,
        count: Number(count)
      }));
      return items.sort((a, b) => b.count - a.count);
    } catch (error) {
      console.error('Error in getDistinctPmTypes:', error);
      throw error;
    }
  }

  async search(searchDto: SearchQuestionDto) {
    const { title, category } = searchDto;
    const where: any = {};
    if (title) {
      where.title = { ilike: `%${title}%` };
    }
    if (category) {
      where.category = category;
    }
    try {
      const data = await this.databaseClient.query<any>('questions', {
        where,
        join: [
          {
            table: 'company',
            on: { 'questions.company_id': 'company.id' },
            select: ['name']
          },
          {
            table: 'pm_type',
            on: { 'questions.pm_type_id': 'pm_type.id' },
            select: ['name']
          }
        ]
      });
      const transformedData = data.map(item => {
        let companyName = '';
        let pmTypeName = '';
        if (item['company'] && item['company'].name) companyName = item['company'].name;
        if (item['pm_type'] && item['pm_type'].name) pmTypeName = item['pm_type'].name;
        return {
          id: item['id'],
          title: item['title'],
          companyId: item['company_id'] ?? null,
          companyName,
          pmTypeId: item['pm_type_id'] ?? null,
          pmTypeName,
          createdAt: item['createdAt']
        };
      });
      return {
        data: transformedData,
        meta: {
          total: data.length
        }
      };
    } catch (error) {
      console.error('Error in search:', error);
      throw error;
    }
  }

  async createBatch(createQuestionDtos: CreateQuestionDto[]): Promise<Question[]> {
    const results: Question[] = [];
    for (const dto of createQuestionDtos) {
      const now = new Date();
      const questionData = {
        ...dto,
        company_id: dto.companyId,
        pm_type_id: dto.pmTypeId,
        createdAt: now,
        updatedAt: now,
      };
      if (typeof dto.keyPoints === 'string') {
        questionData.keyPoints = [dto.keyPoints];
      }
      if (typeof dto.category === 'string') {
        questionData.category = [dto.category];
      }

      const result = await this.databaseService.create('questions', questionData as ObjectLiteral);
      results.push(this.mapToQuestion(result));
    }
    return results;
  }

  private mapToQuestion(item: ObjectLiteral): Question {
    const parseStringArray = (value: any): string[] => {
        if (Array.isArray(value)) return value.map(String);
        if (typeof value === 'string') {
            if (value.startsWith('{') && value.endsWith('}')) {
                return value.substring(1, value.length - 1).split(',').map(s => s.trim()).filter(s => s);
            }
            try {
                const parsed = JSON.parse(value);
                if (Array.isArray(parsed)) return parsed.map(String);
            } catch (e) { /* not a JSON array */ }
            return [value];
        }
        return value == null ? [] : [String(value)];
    };

    // 只取 join 结果
    const companyName = (item['company'] && item['company'].name) ?? null;
    const pmTypeName = (item['pm_type'] && item['pm_type'].name) ?? null;

    const question: Question = {
      id: item['id'] as string,
      title: item['title'] as string,
      content: item['content'] as string,
      keyPoints: parseStringArray(item['keyPoints']),
      answer: item['answer'] as string,
      tips: item['tips'] as string | null,
      companyId: item['company_id'] === undefined ? null : (item['company_id'] as number | null),
      companyName,
      category: parseStringArray(item['category']),
      pmTypeId: item['pm_type_id'] === undefined ? null : (item['pm_type_id'] as number | null),
      pmTypeName,
      createdAt: typeof item['createdAt'] === 'string' ? item['createdAt'] : (item['createdAt'] as Date)?.toISOString(),
      updatedAt: typeof item['updatedAt'] === 'string' ? item['updatedAt'] : (item['updatedAt'] as Date)?.toISOString(),
    };
    return question;
  }
}

function глобальнийУнікальнийІдентифікатор(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
} 