import { <PERSON>tity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Company } from '../company/company.entity';
import { PmType } from '../pm-type/pm-type.entity';

@Entity('questions')
export class Question {
  @ApiProperty({ description: '问题ID' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: '问题标题' })
  @Column()
  title: string;

  @ApiProperty({ description: '问题内容' })
  @Column('text')
  content: string;

  @ApiProperty({ description: '关键点', type: [String] })
  @Column('text', { array: true, nullable: true })
  keyPoints: string[];

  @ApiProperty({ description: '答案' })
  @Column('text')
  answer: string;

  @ApiPropertyOptional({ description: '技巧提示' })
  @Column('text', { nullable: true })
  tips: string | null;

  @ApiPropertyOptional({ description: '公司ID' })
  @Column({ name: 'company_id', nullable: true })
  companyId: number | null;

  @ApiPropertyOptional({ description: '公司名称' })
  @Column({ name: 'company_name', nullable: true })
  companyName: string | null;

  @ApiProperty({ description: '分类', type: [String] })
  @Column('text', { array: true, nullable: true })
  category: string[];

  @ApiPropertyOptional({ description: '产品经理类型ID' })
  @Column({ name: 'pm_type_id', nullable: true })
  pmTypeId: number | null;

  @ApiPropertyOptional({ description: '产品经理类型名称' })
  @Column({ name: 'pm_type_name', nullable: true })
  pmTypeName: string | null;

  @ApiProperty({ description: '创建时间' })
  @CreateDateColumn()
  createdAt: string;

  @ApiProperty({ description: '更新时间' })
  @UpdateDateColumn()
  updatedAt: string;
} 