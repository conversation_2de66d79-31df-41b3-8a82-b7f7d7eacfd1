import { Modu<PERSON> } from '@nestjs/common';
import { QuestionsService } from './questions.service';
import { QuestionsController } from './questions.controller';
import { Question } from './question.entity';
import { DatabaseModule } from '../databases/database.module';
import { SearchHistoryModule } from '../search-history/search-history.module';

@Module({
  imports: [
    DatabaseModule,
    SearchHistoryModule,
  ],
  controllers: [QuestionsController],
  providers: [QuestionsService],
})
export class QuestionsModule {} 