import { Controller, Get, Post, Body, Patch, Param, Delete, Query, UseInterceptors, UseGuards, Request, Put } from '@nestjs/common';
import { QuestionsService } from './questions.service';
import { CreateQuestionDto } from './create-question.dto';
import { UpdateQuestionDto } from './update-question.dto';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { Question } from './question.entity';
import { TransformInterceptor } from '../interceptors/transform.interceptor';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { SearchHistoryService } from '../search-history/search-history.service';

interface CountItem {
  name: string;
  count: number;
}

@ApiTags('questions')
@Controller('questions')
@UseInterceptors(TransformInterceptor)
export class QuestionsController {
  constructor(
    private readonly questionsService: QuestionsService,
    private readonly searchHistoryService: SearchHistoryService,
  ) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '创建新的面试题' })
  @ApiResponse({ status: 201, description: '面试题创建成功', type: Question })
  create(@Body() createQuestionDto: CreateQuestionDto) {
    return this.questionsService.create(createQuestionDto);
  }

  @Post('batch')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '批量创建面试题' })
  @ApiResponse({ status: 201, description: '批量创建面试题成功', type: Question, isArray: true })
  async createBatch(@Body() batchDto: import('./create-question.dto').CreateQuestionsBatchDto) {
    return this.questionsService.createBatch(batchDto.questions);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '获取面试题列表' })
  @ApiResponse({ status: 200, description: '成功获取面试题列表', type: Question, isArray: true })
  @ApiQuery({ name: 'companyId', required: false, description: '公司ID' })
  @ApiQuery({ name: 'category', required: false, description: '分类' })
  @ApiQuery({ name: 'search', required: false, description: '搜索关键词' })
  @ApiQuery({ name: 'pmTypeId', required: false, description: '产品经理类型ID' })
  @ApiQuery({ name: 'page', required: false, description: '页码', type: Number })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量', type: Number })
  @ApiQuery({ name: 'sort', required: false, description: '排序字段' })
  @ApiQuery({ name: 'order', required: false, description: '排序方式', enum: ['asc', 'desc'] })
  async findAll(
    @Query('companyId') companyId?: number,
    @Query('category') category?: string,
    @Query('search') search?: string,
    @Query('pmTypeId') pmTypeId?: number,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('sort') sort?: string,
    @Query('order') order?: 'asc' | 'desc',
  ) {
    // 解码 URL 编码的参数
    const decodedCategory = category ? decodeURIComponent(category) : undefined;
    const decodedSearch = search ? decodeURIComponent(search) : undefined;

    const result = await this.questionsService.findAll({
      companyId: companyId ? Number(companyId) : undefined,
      category: decodedCategory,
      search: decodedSearch,
      pmTypeId: pmTypeId ? Number(pmTypeId) : undefined,
      page: page ? Number(page) : undefined,
      limit: limit ? Number(limit) : undefined,
      sort,
      order,
    });

    // 过滤掉 answer 和 content 字段
    const filteredList = result.data.map(({ answer, content, ...rest }) => rest);

    return {
      list: filteredList,
      total: result.meta.total,
      page: result.meta.page,
      limit: result.meta.limit,
      totalPages: result.meta.totalPages,
    };
  }

  @Get('companies')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '获取所有不重复的公司名称列表' })
  @ApiResponse({ status: 200, description: '成功获取公司名称列表', type: [String] })
  async getCompanies(): Promise<CountItem[]> {
    const companies = await this.questionsService.getDistinctCompanies();
    return companies;
  }

  @Get('pm-types')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '获取所有不重复的产品经理类型列表' })
  @ApiResponse({ status: 200, description: '成功获取产品经理类型列表', type: [String] })
  async getPmTypes(): Promise<CountItem[]> {
    const pmTypes = await this.questionsService.getDistinctPmTypes();
    return pmTypes;
  }

  @Get('search')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '搜索面试题' })
  @ApiResponse({ status: 200, description: '成功搜索面试题', type: [Question] })
  @ApiQuery({ name: 'title', required: false, description: '标题关键词' })
  @ApiQuery({ name: 'category', required: false, description: '题目类别' })
  async search(
    @Request() req,
    @Query('title') title?: string,
    @Query('category') category?: string
  ) {
    const result = await this.questionsService.search({ title, category });
    
    // 记录搜索历史
    if (title) {
      await this.searchHistoryService.createSearchHistory(req.user.id, title, category);
    }

    return {
      list: result.data,
      total: result.meta.total
    };
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '获取指定面试题' })
  @ApiResponse({ status: 200, description: '成功获取面试题', type: Question })
  @ApiParam({ name: 'id', description: '面试题ID' })
  findOne(@Param('id') id: string) {
    return this.questionsService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '更新面试题' })
  @ApiResponse({ status: 200, description: '面试题更新成功', type: Question })
  @ApiParam({ name: 'id', description: '面试题ID' })
  update(@Param('id') id: string, @Body() updateQuestionDto: UpdateQuestionDto) {
    return this.questionsService.update(id, updateQuestionDto);
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '更新面试题' })
  @ApiResponse({ status: 200, description: '面试题更新成功', type: Question })
  @ApiParam({ name: 'id', description: '面试题ID' })
  updateWithPut(@Param('id') id: string, @Body() updateQuestionDto: UpdateQuestionDto) {
    return this.questionsService.update(id, updateQuestionDto);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '删除面试题' })
  @ApiResponse({ status: 200, description: '面试题删除成功' })
  @ApiParam({ name: 'id', description: '面试题ID' })
  remove(@Param('id') id: string) {
    return this.questionsService.remove(id);
  }
} 