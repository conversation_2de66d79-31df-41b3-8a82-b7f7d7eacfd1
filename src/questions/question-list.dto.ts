import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class QuestionListDto {
  @ApiProperty({ description: '问题ID' })
  id: string;

  @ApiProperty({ description: '问题标题' })
  title: string;

  @ApiProperty({ description: '公司名称' })
  company: string;

  @ApiProperty({ description: '分类', type: [String] })
  category: string[];

  @ApiProperty({ description: '产品经理类型' })
  pmType: string;

  @ApiProperty({ description: '创建时间' })
  @Transform(({ value }) => new Date(value))
  created_at: Date;
} 