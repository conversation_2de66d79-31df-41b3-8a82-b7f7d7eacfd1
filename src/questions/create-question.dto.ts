import { IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ptional, IsN<PERSON>ber } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateQuestionDto {
  @ApiProperty({ description: '问题标题' })
  @IsString()
  title: string;

  @ApiPropertyOptional({ description: '问题内容' })
  @IsString()
  @IsOptional()
  content?: string;

  @ApiPropertyOptional({ description: '关键点', type: [String] })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  keyPoints?: string[];

  @ApiProperty({ description: '答案' })
  @IsString()
  answer: string;

  @ApiPropertyOptional({ description: '技巧提示' })
  @IsString()
  @IsOptional()
  tips?: string;

  @ApiPropertyOptional({ description: '公司ID' })
  @IsNumber()
  @IsOptional()
  companyId?: number;

  @ApiPropertyOptional({ description: '分类', type: [String] })
  @Is<PERSON>rray()
  @IsString({ each: true })
  @IsOptional()
  category?: string[];

  @ApiPropertyOptional({ description: '产品经理类型ID' })
  @IsNumber()
  @IsOptional()
  pmTypeId?: number;
}

export class CreateQuestionsBatchDto {
  @ApiProperty({ description: '面试题列表', type: [CreateQuestionDto] })
  @IsArray()
  questions: CreateQuestionDto[];
}
