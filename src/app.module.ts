import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { QuestionsModule } from './questions/questions.module';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { SearchHistoryModule } from './search-history/search-history.module';
import { DatabaseModule } from './databases/database.module';
import { FavoritesModule } from './favorites/favorites.module';
import { QuestionViewsModule } from './question-views/question-views.module';
import { CompanyModule } from './company/company.module';
import { PmTypeModule } from './pm-type/pm-type.module';
import jwtConfig from './config/jwt.config';
import { CacheControlMiddleware } from './middleware/cache-control.middleware';
import { AiProxyModule } from './ai-proxy/ai-proxy.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [jwtConfig],
    }),
    DatabaseModule.forRoot('supabase'),
    // DatabaseModule.forRoot('typeorm'),
    QuestionsModule,
    AuthModule,
    UsersModule,
    FavoritesModule,
    SearchHistoryModule,
    QuestionViewsModule,
    CompanyModule,
    PmTypeModule,
    AiProxyModule,
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(CacheControlMiddleware).forRoutes('*');
  }
}
