import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

// 只对 GET 请求设置缓存头
@Injectable()
export class CacheControlMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    if (req.method === 'GET') {
      res.setHeader('Cache-Control', 's-maxage=259200, stale-while-revalidate');
    }
    next();
  }
} 