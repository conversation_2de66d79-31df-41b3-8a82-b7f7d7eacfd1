import { Injectable, Logger } from "@nestjs/common";
import ModelClient, { isUnexpected } from "@azure-rest/ai-inference";
import { AzureKeyCredential } from "@azure/core-auth";

@Injectable()
export class AiProxyService {
  private readonly logger = new Logger(AiProxyService.name);

  async proxyChat(params: {
    messages: { role: string; content: string }[];
    model?: string;
    temperature?: number;
    top_p?: number;
  }) {
    try {
      const token = process.env.AI_API_KEY;
      const endpoint = process.env.AI_ENDPOINT;

      if (!token || !endpoint) {
        throw new Error('AI API configuration is missing');
      }

      const model = params.model || "openai/gpt-4.1";
      this.logger.debug(`Using model: ${model}, endpoint: ${endpoint}`);

      const client = ModelClient(endpoint, new AzureKeyCredential(token));

      const requestBody = {
        messages: params.messages,
        temperature: params.temperature ?? 1,
        top_p: params.top_p ?? 1,
        model: model
      };
      this.logger.debug(`Request body: ${JSON.stringify(requestBody)}`);

      const response = await client.path("/chat/completions").post({
        body: requestBody,
      });

      if (isUnexpected(response)) {
        const errorDetails = {
          status: response.status,
          body: response.body,
          headers: response.headers,
        };
        this.logger.error(`AI API error details: ${JSON.stringify(errorDetails)}`);
        throw new Error(`AI API request failed: ${response.status}`);
      }

      return response.body;
    } catch (error) {
      this.logger.error(`Error in proxyChat: ${error.message}`, error.stack);
      if (error.response) {
        this.logger.error(`Response error: ${JSON.stringify(error.response)}`);
      }
      throw error;
    }
  }
} 