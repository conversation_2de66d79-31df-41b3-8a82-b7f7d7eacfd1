import { Controller, Post, Body, UseGuards } from "@nestjs/common";
import { JwtAuthGuard } from "../guards/jwt-auth.guard";
import { AiProxyService } from "./ai-proxy.service";

@UseGuards(JwtAuthGuard)
@Controller("ai-proxy")
export class AiProxyController {
  constructor(private readonly aiProxyService: AiProxyService) {}

  @Post("chat")
  async proxyChat(@Body() body: {
    messages: { role: string; content: string }[];
    model?: string;
    temperature?: number;
    top_p?: number;
  }) {
    return this.aiProxyService.proxyChat(body);
  }
} 