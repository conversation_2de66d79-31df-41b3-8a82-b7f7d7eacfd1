import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ValidationPipe } from '@nestjs/common';
import { TransformInterceptor } from './interceptors/transform.interceptor';
import { HttpExceptionFilter } from './filters/http-exception.filter';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  // 启用全局验证管道
  app.useGlobalPipes(new ValidationPipe());
  
  // 注册全局拦截器
  app.useGlobalInterceptors(new TransformInterceptor());
  
  // 注册全局异常过滤器
  app.useGlobalFilters(new HttpExceptionFilter());
  
  // 配置 Swagger
  const config = new DocumentBuilder()
    .setTitle('产品经理面试题库 API')
    .setDescription('产品经理面试题库系统的 API 文档')
    .setVersion('1.0')
    .addTag('questions', '面试问题相关接口')
    .addTag('users', '用户相关接口')
    .addBearerAuth()
    .build();
  
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);
  
  await app.listen(process.env.PORT ?? 3000);
}
bootstrap();
