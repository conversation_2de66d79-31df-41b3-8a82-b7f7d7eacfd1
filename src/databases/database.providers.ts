import { ConfigService } from '@nestjs/config';
import { TypeOrmDatabaseService } from './typeorm/database.service';
import { SupabaseDatabaseService } from './supabase/database.service';

// Export DATABASE_CLIENT
export const DATABASE_CLIENT = 'DATABASE_CLIENT';

export const databaseProviders = [
  {
    provide: DATABASE_CLIENT,
    useFactory: (configService: ConfigService) => {
      const dbType = configService.get<string>('DB_TYPE');
      if (dbType === 'supabase') {
        return new SupabaseDatabaseService();
      }
      return new TypeOrmDatabaseService();
    },
    inject: [ConfigService],
  },
]; 