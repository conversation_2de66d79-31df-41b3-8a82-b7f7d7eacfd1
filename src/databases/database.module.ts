// databases/database.module.ts
import { DynamicModule, Module } from '@nestjs/common';
import { TypeOrmDatabaseService } from './typeorm/database.service';
import { SupabaseDatabaseService } from './supabase/database.service';
import { DatabaseService } from './database.service';
import { DATABASE_CLIENT } from '../interfaces/database.interface';

@Module({
  providers: [
    {
      provide: DATABASE_CLIENT,
      useClass: process.env.DATABASE_TYPE === 'typeorm' 
        ? TypeOrmDatabaseService 
        : SupabaseDatabaseService,
    },
    DatabaseService,
  ],
  exports: [DATABASE_CLIENT, DatabaseService],
})
export class DatabaseModule {
  static forRoot(dbType: 'typeorm' | 'supabase'): DynamicModule {
    const providers = [
      {
        provide: DATABASE_CLIENT,
        useClass: dbType === 'typeorm' ? TypeOrmDatabaseService : SupabaseDatabaseService,
      },
      DatabaseService,
    ];

    return {
      module: DatabaseModule,
      providers,
      exports: providers,
      global: true,
    };
  }
}