import { Injectable, Inject } from '@nestjs/common';
import { DatabaseClient, DATABASE_CLIENT } from '../interfaces/database.interface';
import { NotFoundException } from '@nestjs/common';
import { DatabaseService as IDatabaseService, DatabaseClient as IDatabaseClient, FindOptionsWhere } from '../interfaces/database.interface';
import { Logger } from '@nestjs/common';
import { ObjectLiteral } from 'typeorm';

@Injectable()
export class DatabaseService implements IDatabaseService {
  private readonly logger = new Logger(DatabaseService.name);

  constructor(
    @Inject(DATABASE_CLIENT)
    private readonly databaseClient: IDatabaseClient,
  ) {}

  async create<T extends ObjectLiteral>(table: string, data: Partial<T>): Promise<T> {
    return this.databaseClient.insert<T>(table, data);
  }

  async find<T extends ObjectLiteral>(table: string, options: {
    where?: FindOptionsWhere<T>;
    limit?: number;
    offset?: number;
    orderBy?: Record<string, 'asc' | 'desc'>;
    select?: string;
    join?: import('../interfaces/database.interface').JoinOption[];
  } = {}): Promise<T[]> {
    try {
      const { where = {}, limit, offset, orderBy, select, join } = options;
      const queryOptions: any = {};

      if (select) {
        queryOptions.select = select;
      }

      if (Object.keys(where).length > 0) {
        queryOptions.where = where as Record<string, any>;
      }

      if (limit !== undefined) {
        queryOptions.limit = limit;
      }
      if (offset !== undefined) {
        queryOptions.offset = offset;
      }

      if (orderBy) {
        const [column, order] = Object.entries(orderBy)[0];
        queryOptions.order = {
          column,
          ascending: order === 'asc'
        };
      }

      if (join) {
        queryOptions.join = join;
      }

      return this.databaseClient.query<T>(table, queryOptions);
    } catch (error) {
      this.logger.error(`Error in find method for table ${table}:`, error);
      throw error;
    }
  }

  async findOne<T extends ObjectLiteral>(table: string, options?: { where?: FindOptionsWhere<T> }): Promise<T | null> {
    const results = await this.find<T>(table, { ...(options || {}), limit: 1 });
    return results.length > 0 ? results[0] : null;
  }

  async update<T extends ObjectLiteral>(table: string, id: string, data: Partial<T>): Promise<T> {
    return this.databaseClient.update<T>(table, id, data);
  }

  async delete<T extends ObjectLiteral>(table: string, options: { where: FindOptionsWhere<T> }): Promise<void> {
    const whereClause = options.where as any;
    if (whereClause.id && typeof whereClause.id === 'string') {
      return this.databaseClient.delete(table, whereClause.id);
    }
    this.logger.error('Complex delete for DatabaseService.delete not fully implemented for non-ID based criteria without DatabaseClient modification.');
    throw new Error('Complex delete not implemented for non-ID based criteria or non-string ID without DatabaseClient modification.');
  }

  async count(table: string, where?: Record<string, any>): Promise<number> {
    try {
      return await this.databaseClient.count(table, { where });
    } catch (error) {
      this.logger.error(`Error in count method for table ${table} with where ${JSON.stringify(where)}:`, error);
      throw error;
    }
  }
} 