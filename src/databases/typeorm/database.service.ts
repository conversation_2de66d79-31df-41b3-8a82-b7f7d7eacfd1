// databases/typeorm/database.service.ts
import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { DataSource, Repository as TypeOrmRepository, ObjectLiteral, DeepPartial, FindOptionsWhere } from 'typeorm';
import { DatabaseClient } from '../../interfaces/database.interface';
import { Repository } from '../../interfaces/repository.interface';
import { typeOrmConfig } from '../../config/database.config';
import { Question } from '../../questions/question.entity';
import { User } from '../../users/users.entity';

@Injectable()
export class TypeOrmDatabaseService implements DatabaseClient, OnModuleInit, OnModuleDestroy {
  private dataSource: DataSource;
  private entityMap: Record<string, new () => ObjectLiteral> = {
    questions: Question,
    users: User,
  };

  constructor() {
    this.dataSource = new DataSource({
      ...typeOrmConfig as any,
      entities: [Question, User],
      synchronize: true, // 开发环境使用，生产环境请关闭
    });
  }

  async onModuleInit() {
    if (!this.dataSource.isInitialized) {
      await this.dataSource.initialize();
    }
  }

  async onModuleDestroy() {
    if (this.dataSource.isInitialized) {
      await this.dataSource.destroy();
    }
  }

  async query<T extends ObjectLiteral>(table: string, options: {
    select?: string;
    where?: Record<string, any>;
    order?: { column: string; ascending?: boolean };
    limit?: number;
    offset?: number;
    join?: import('../../interfaces/database.interface').JoinOption[];
  } = {}): Promise<T[]> {
    const entityClass = this.entityMap[table];
    if (!entityClass) {
      throw new Error(`No entity found for table: ${table}`);
    }
    const repository = this.dataSource.getRepository(entityClass);
    let query = repository.createQueryBuilder(table);

    // 处理 join 查询
    if (options.join && Array.isArray(options.join)) {
      for (const joinOpt of options.join) {
        // join 类型，默认 left
        const joinType = joinOpt.type === 'inner' ? 'innerJoin' : 'leftJoin';
        // 别名
        const alias = joinOpt.alias || joinOpt.table;
        // 连接条件（只支持单字段等值连接）
        const onEntries = Object.entries(joinOpt.on);
        if (onEntries.length === 0) continue;
        // 只支持第一个连接条件
        const [left, right] = onEntries[0];
        // 生成 join 语句
        query = query[joinType](
          `${joinOpt.table}`,
          alias,
          `${left} = ${right}`
        );
        // 处理 select 字段
        if (joinOpt.select && joinOpt.select.length > 0) {
          for (const field of joinOpt.select) {
            query = query.addSelect(`${alias}.${field}`, `${alias}_${field}`);
          }
        }
      }
    }

    // 处理 select 字段
    if (options.select) {
      query = query.select(options.select);
    }

    // 处理 where 条件
    if (options.where) {
      Object.entries(options.where).forEach(([key, value]) => {
        query = query.andWhere(`${table}.${key} = :${key}`, { [key]: value });
      });
    }

    // 处理排序
    if (options.order) {
      query = query.orderBy(`${table}.${options.order.column}`, options.order.ascending ? 'ASC' : 'DESC');
    }

    // 处理分页
    if (options.limit) {
      query = query.take(options.limit);
    }
    if (options.offset) {
      query = query.skip(options.offset);
    }

    // 返回结果
    return query.getRawMany() as Promise<T[]>;
  }

  async insert<T extends ObjectLiteral>(table: string, data: Partial<T>): Promise<T> {
    const entityClass = this.entityMap[table];
    if (!entityClass) {
      throw new Error(`No entity found for table: ${table}`);
    }
    const repository = this.dataSource.getRepository(entityClass);
    const entity = repository.create(data);
    return repository.save(entity) as Promise<T>;
  }

  async update<T extends ObjectLiteral>(table: string, id: string, data: Partial<T>): Promise<T> {
    const entityClass = this.entityMap[table];
    if (!entityClass) {
      throw new Error(`No entity found for table: ${table}`);
    }
    const repository = this.dataSource.getRepository(entityClass);
    await repository.update(id, data);
    return repository.findOneBy({ id } as any) as Promise<T>;
  }

  async delete<T extends ObjectLiteral>(table: string, id: string): Promise<void> {
    const entityClass = this.entityMap[table];
    if (!entityClass) {
      throw new Error(`No entity found for table: ${table}`);
    }
    const repository = this.dataSource.getRepository(entityClass);
    await repository.delete(id);
  }

  async count(table: string, options?: { where?: Record<string, any> }): Promise<number> {
    const entityClass = this.entityMap[table];
    if (!entityClass) {
      throw new Error(`No entity found for table: ${table}`);
    }
    const repository = this.dataSource.getRepository(entityClass);
    
    try {
      const countOptions: { where?: FindOptionsWhere<ObjectLiteral> } = {};
      if (options?.where) {
        countOptions.where = options.where as FindOptionsWhere<ObjectLiteral>;
      }
      return await repository.count(countOptions);
    } catch (error) {
      console.error(`TypeORM count error for table ${table}:`, error);
      throw error;
    }
  }

  async connect(): Promise<void> {
    if (!this.dataSource.isInitialized) {
      await this.dataSource.initialize();
    }
  }

  async disconnect(): Promise<void> {
    if (this.dataSource.isInitialized) {
      await this.dataSource.destroy();
    }
  }

  getRepository<T extends ObjectLiteral>(entity: new () => T): Repository<T> {
    const repo = this.dataSource.getRepository(entity);
    return new TypeOrmRepositoryAdapter<T>(repo);
  }
}

// TypeORM 具体实现适配器
class TypeOrmRepositoryAdapter<T extends ObjectLiteral> implements Repository<T> {
  constructor(private readonly repository: TypeOrmRepository<T>) {}

  async findAll(): Promise<T[]> {
    return this.repository.find();
  }

  async findById(id: string): Promise<T | null> {
    return this.repository.findOneBy({ id } as any);
  }

  async create(data: Partial<T>): Promise<T> {
    const entity = this.repository.create(data as any);
    const saved = await this.repository.save(entity);
    return Array.isArray(saved) ? saved[0] : saved;
  }

  async update(id: string, data: Partial<T>): Promise<T> {
    await this.repository.update(id, data);
    const result = await this.repository.findOneBy({ id } as any);
    if (!result) {
      throw new Error(`Entity with id ${id} not found`);
    }
    return result;
  }

  async delete(id: string): Promise<void> {
    await this.repository.delete(id);
  }
}