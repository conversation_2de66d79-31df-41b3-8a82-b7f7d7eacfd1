// databases/supabase/database.service.ts
import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { DatabaseClient } from '../../interfaces/database.interface';
import { supabaseConfig } from '../../config/supabase.config';
import fetch from 'node-fetch';
import { FieldMapper } from '../../util/field-mapper.util';

interface WhereOperator {
  eq?: any;
  neq?: any;
  gt?: any;
  gte?: any;
  lt?: any;
  lte?: any;
  like?: string;
  ilike?: string;
  in?: any[];
  not?: any;
  or?: Array<Record<string, WhereOperator>>;
}

@Injectable()
export class SupabaseDatabaseService implements DatabaseClient, OnModuleInit, OnModuleDestroy {
  protected supabase: SupabaseClient;

  constructor() {
    if (!supabaseConfig.url || !supabaseConfig.key) {
      throw new Error('Supabase URL and key must be defined');
    }
    this.supabase = createClient<any, "public", any>(
      supabaseConfig.url,
      supabaseConfig.key,
      {
        auth: {
          persistSession: false,
          autoRefreshToken: false,
          detectSessionInUrl: false
        },
        global: {
          headers: {
            'Content-Type': 'application/json'
          },
          fetch: fetch as any
        },
        db: {
          schema: 'public'
        },
      },
    );
  }

  async onModuleInit() {
    // 测试连接
    try {
      const { data, error } = await this.supabase.from('questions').select('count').limit(1);
      if (error) {
        console.error('Supabase connection test failed:', error);
        throw error;
      }
      console.log('Successfully connected to Supabase');
    } catch (error) {
      console.error('Failed to connect to Supabase:', error);
      // 不要在这里抛出错误，让应用继续启动
      // 这样即使数据库连接失败，应用仍然可以启动
    }
  }

  async onModuleDestroy() {
    // Supabase 客户端会自动处理清理
  }

  private handleWhereCondition(query: any, where: Record<string, WhereOperator | any>) {
    Object.entries(where).forEach(([key, value]) => {
      if (typeof value === 'object' && value !== null) {
        if (value.ilike !== undefined) {
          query = query.ilike(key, value.ilike);
        } else if (value.like !== undefined) {
          query = query.like(key, value.like);
        } else if (value.eq !== undefined) {
          query = query.eq(key, value.eq);
        } else if (value.neq !== undefined) {
          query = query.neq(key, value.neq);
        } else if (value.gt !== undefined) {
          query = query.gt(key, value.gt);
        } else if (value.gte !== undefined) {
          query = query.gte(key, value.gte);
        } else if (value.lt !== undefined) {
          query = query.lt(key, value.lt);
        } else if (value.lte !== undefined) {
          query = query.lte(key, value.lte);
        } else if (value.in !== undefined) {
          query = query.in(key, value.in);
        } else if (value.not !== undefined) {
          query = query.not(key, value.not);
        } else if (value.or !== undefined) {
          const orConditions = value.or.map((condition: Record<string, any>) => {
            return this.handleWhereCondition(this.supabase.from(query.table), condition);
          });
          query = query.or(orConditions.map((q: any) => q.toSQL().sql).join(' OR '));
        }
      } else {
        query = query.eq(key, value);
      }
    });
    return query;
  }

  async query<T>(table: string, options: {
    select?: string;
    where?: Record<string, any>;
    order?: { column: string; ascending?: boolean };
    limit?: number;
    offset?: number;
    join?: import('../../interfaces/database.interface').JoinOption[];
  } = {}): Promise<T[]> {
    try {
      // 构建 select 字段，支持 supabase 的外键 join 语法
      let select = options.select || '*';
      if (options.join && Array.isArray(options.join) && options.join.length > 0) {
        // 只支持外键 join，不能自定义 join 类型、别名、on 条件
        // 语法如 questions(*,company(name),pm_type(name))
        const joinSelects = options.join.map(j => {
          // 只支持 select 字段
          if (j.select && j.select.length > 0) {
            return `${j.table}(${j.select.join(',')})`;
          }
          return `${j.table}(*)`;
        });
        select = [select, ...joinSelects].join(',');
      }

      let query = this.supabase
        .from(table)
        .select(select);

      if (options.where) {
        query = this.handleWhereCondition(query, options.where);
      }

      if (options.order) {
        query = query.order(options.order.column, { ascending: options.order.ascending });
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      if (options.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
      }

      const { data, error } = await query;

      if (error) {
        console.error(`Supabase query error for table ${table}:`, error);
        throw error;
      }

      return data as T[];
    } catch (error) {
      console.error(`Failed to query table ${table}:`, error);
      throw error;
    }
  }

  async insert<T>(table: string, data: Partial<T>): Promise<T> {
    try {
      // 使用 FieldMapper 处理字段映射
      const mappedData = FieldMapper.toDatabase(table, data);

      const { data: result, error } = await this.supabase
        .from(table)
        .insert(mappedData)
        .select()
        .single();

      if (error) {
        console.error(`Supabase insert error for table ${table}:`, error);
        throw error;
      }

      // 将返回的数据转换回实体格式
      return FieldMapper.toEntity(table, result) as T;
    } catch (error) {
      console.error(`Failed to insert into table ${table}:`, error);
      throw error;
    }
  }

  async update<T>(table: string, id: string, data: Partial<T>): Promise<T> {
    try {
      const { data: result, error } = await this.supabase
        .from(table)
        .update(data)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error(`Supabase update error for table ${table}:`, error);
        throw error;
      }
      return result as T;
    } catch (error) {
      console.error(`Failed to update table ${table}:`, error);
      throw error;
    }
  }

  async delete<T>(table: string, id: string): Promise<void> {
    try {
      const { error } = await this.supabase
        .from(table)
        .delete()
        .eq('id', id);

      if (error) {
        console.error(`Supabase delete error for table ${table}:`, error);
        throw error;
      }
    } catch (error) {
      console.error(`Failed to delete from table ${table}:`, error);
      throw error;
    }
  }

  async count(table: string, options?: { where?: Record<string, WhereOperator | any> }): Promise<number> {
    try {
      let query = this.supabase
        .from(table)
        .select('*', { count: 'exact', head: true });

      if (options?.where) {
        query = this.handleWhereCondition(query, options.where);
      }

      const { count, error } = await query;

      if (error) {
        console.error(`Supabase count error for table ${table}:`, error);
        throw error;
      }

      return count === null ? 0 : count;
    } catch (error) {
      console.error(`Failed to count table ${table}:`, error);
      throw error;
    }
  }

  async connect(): Promise<void> {
    // 测试连接
    try {
      const { data, error } = await this.supabase.from('questions').select('count').limit(1);
      if (error) {
        console.error('Supabase connection test failed:', error);
        throw error;
      }
      console.log('Successfully connected to Supabase');
    } catch (error) {
      console.error('Failed to connect to Supabase:', error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    // Supabase 客户端会自动处理清理
  }
}