import * as bcrypt from 'bcrypt';
import * as fs from 'fs';
import * as path from 'path';

async function generateHashedPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 10);
}

async function generateSeedData() {
  const passwords = {
    zhangsan: '123456',
    lisi: '654321',
    wangwu: '888888'
  };

  const hashedPasswords = await Promise.all(
    Object.values(passwords).map(password => generateHashedPassword(password))
  );

  const seedData = `-- 插入测试用户数据
INSERT INTO "user" (name, email, "phoneNumber", password, "createdAt", "updatedAt")
VALUES 
  -- 手机号注册用户
  (
    '张三',
    '<EMAIL>',
    '13800138001',
    '${hashedPasswords[0]}', -- 实际密码: ${passwords.zhangsan}
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
  ),
  -- 邮箱注册用户
  (
    '李四',
    '<EMAIL>',
    NULL,
    '${hashedPasswords[1]}', -- 实际密码: ${passwords.lisi}
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
  ),
  -- 同时有手机号和邮箱的用户
  (
    '王五',
    '<EMAIL>',
    '13800138003',
    '${hashedPasswords[2]}', -- 实际密码: ${passwords.wangwu}
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
  );`;

  const seedPath = path.join(__dirname, 'seed.sql');
  fs.writeFileSync(seedPath, seedData);
  console.log('测试数据已生成到:', seedPath);
}

generateSeedData().catch(console.error); 