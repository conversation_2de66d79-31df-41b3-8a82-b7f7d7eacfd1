-- 创建 search_history 表
CREATE TABLE search_history (
    id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id),
    query TEXT NOT NULL,
    category TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- 添加外键约束
    CONSTRAINT fk_user
        FOREIGN KEY (user_id)
        REFERENCES auth.users(id)
        ON DELETE CASCADE
);

-- 创建索引以提高查询性能
CREATE INDEX idx_search_history_user_id ON search_history(user_id);
CREATE INDEX idx_search_history_created_at ON search_history(created_at);

-- 添加 RLS (Row Level Security) 策略
ALTER TABLE search_history ENABLE ROW LEVEL SECURITY;

-- 创建策略：用户只能查看自己的搜索历史
CREATE POLICY "Users can view their own search history"
    ON search_history
    FOR SELECT
    USING (auth.uid() = user_id);

-- 创建策略：用户只能插入自己的搜索历史
CREATE POLICY "Users can insert their own search history"
    ON search_history
    FOR INSERT
    WITH CHECK (auth.uid() = user_id);

-- 创建策略：用户只能删除自己的搜索历史
CREATE POLICY "Users can delete their own search history"
    ON search_history
    FOR DELETE
    USING (auth.uid() = user_id);