import { Injectable } from '@nestjs/common';
import { SearchHistory } from './search-history.entity';
import { DatabaseService } from '../databases/database.service';
import { SearchHistoryDto, CreateSearchHistoryDto, SearchHistoryResponseDto } from './search-history.dto';
import { DateUtils } from '../utils/date.utils';

@Injectable()
export class SearchHistoryService {
  constructor(
    private readonly databaseService: DatabaseService,
  ) {}

  async createSearchHistory(userId: string, query: string, category?: string): Promise<SearchHistoryDto> {
    const data = {
      user_id: userId,
      query,
      category,
      created_at: DateUtils.getCurrentTime()
    };

    const result = await this.databaseService.create('search_history', data);
    return this.mapToDto(result);
  }

  async getUserSearchHistory(userId: string): Promise<SearchHistoryResponseDto> {
    const [list, total] = await Promise.all([
      this.databaseService.find('search_history', {
        where: { user_id: userId },
        orderBy: { created_at: 'desc' },
        limit: 10
      }),
      this.databaseService.count('search_history', { user_id: userId })
    ]);

    return {
      list: list.map(item => this.mapToDto(item)),
      total
    };
  }

  private mapToDto(data: any): SearchHistoryDto {
    return {
      id: data.id,
      userId: data.user_id,
      query: data.query,
      category: data.category,
      createdAt: DateUtils.fromDatabase(data.created_at)
    };
  }
} 