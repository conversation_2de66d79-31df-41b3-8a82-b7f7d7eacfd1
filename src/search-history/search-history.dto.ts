import { ApiProperty } from '@nestjs/swagger';

export class SearchHistoryDto {
  @ApiProperty({ description: '搜索历史记录ID' })
  id: number;

  @ApiProperty({ description: '用户ID' })
  userId: number;

  @ApiProperty({ description: '搜索关键词' })
  query: string;

  @ApiProperty({ description: '搜索类别', required: false })
  category?: string;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;
}

export class CreateSearchHistoryDto {
  @ApiProperty({ description: '搜索关键词' })
  query: string;

  @ApiProperty({ description: '搜索类别', required: false })
  category?: string;
}

export class SearchHistoryResponseDto {
  @ApiProperty({ description: '搜索历史记录列表', type: [SearchHistoryDto] })
  list: SearchHistoryDto[];

  @ApiProperty({ description: '总数' })
  total: number;
} 