import { Injectable, UnauthorizedException, Logger } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { DatabaseClient, DATABASE_CLIENT } from '../interfaces/database.interface';
import { Inject } from '@nestjs/common';
import { UserPayload } from './user-payload.interface';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  private readonly logger = new Logger(JwtStrategy.name);

  constructor(
    @Inject(DATABASE_CLIENT)
    private readonly databaseClient: DatabaseClient,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: process.env.JWT_SECRET || 'your-secret-key',
    });
  }

  async validate(payload: UserPayload & { sub: string }) {
    try {
      const users = await this.databaseClient.query('users', {
        where: { id: payload.sub }
      });
      
      if (users.length === 0) {
        this.logger.warn(`User not found for payload: ${JSON.stringify(payload)}`);
        throw new UnauthorizedException('用户不存在或已被删除');
      }
      
      return users[0];
    } catch (error) {
      this.logger.error(`Error validating JWT payload: ${error.message}`, error.stack);
      throw new UnauthorizedException('认证失败');
    }
  }
} 