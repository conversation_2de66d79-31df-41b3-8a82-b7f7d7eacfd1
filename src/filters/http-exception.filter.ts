import { ExceptionFilter, Catch, ArgumentsHost, HttpException, HttpStatus } from '@nestjs/common';
import { Response } from 'express';

interface SupabaseError {
  code: string;
  details: string;
  hint: string | null;
  message: string;
}

@Catch()
export class HttpExceptionFilter implements ExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    
    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'Internal server error';

    if (exception instanceof HttpException) {
      status = exception.getStatus();
      message = exception.message;
    } else if (typeof exception === 'object' && exception !== null) {
      // 处理 Supabase 错误
      const error = exception as { error?: SupabaseError };
      if (error.error) {
        status = HttpStatus.BAD_REQUEST;
        message = error.error.details || error.error.message;
      }
    }

    response
      .status(status)
      .json({
        code: 1,
        data: null,
        msg: message
      });
  }
} 